import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';

/**
 * API endpoint for fetching years
 * Supports:
 * - GET /api/years - Get all years
 * - GET /api/years?name=Year 5 - Get year by name
 * - GET /api/years?yearNumber=5 - Get year by yearNumber
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { name, yearNumber } = req.query;

    // If name is provided, extract the year number and find by yearNumber
    if (name && typeof name === 'string') {
      // Extract year number from the year string (e.g., "Year 5" -> 5)
      const yearMatch = name.match(/\d+/);
      const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;

      if (!yearNumber) {
        return res.status(400).json({ message: 'Invalid year name format. Expected format: "Year X"' });
      }

      const year = await prisma.year.findFirst({
        where: {
          yearNumber: yearNumber,
        },
      });

      if (!year) {
        return res.status(404).json({ message: 'Year not found' });
      }

      return res.status(200).json({ year });
    }

    // If yearNumber is provided, find by yearNumber
    if (yearNumber && typeof yearNumber === 'string') {
      const year = await prisma.year.findFirst({
        where: {
          yearNumber: parseInt(yearNumber, 10),
        },
      });

      if (!year) {
        return res.status(404).json({ message: 'Year not found' });
      }

      return res.status(200).json({ year });
    }

    // If no specific query parameter is provided, return all years
    const years = await prisma.year.findMany({
      orderBy: {
        yearNumber: 'asc',
      },
    });

    res.status(200).json(years);
  } catch (error) {
    console.error('Error fetching years:', error);
    res.status(500).json({ message: 'Failed to fetch years' });
  }
}
