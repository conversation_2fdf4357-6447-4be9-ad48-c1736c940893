import React, { useState } from 'react';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import { QuizProvider } from '../components/quiz/QuizContext';
import AiTutorPanel from '../components/quiz/AiTutorPanel';

// Sample question for testing
const sampleQuestion = {
  id: 1,
  promptEn: 'What is photosynthesis?',
  promptZh: '什么是光合作用？',
  promptMs: 'Apakah fotosintesis?',
  originalLanguage: 'ZH',
  type: 'MULTIPLE_CHOICE',
  choices: [
    { key: 'A', textEn: 'A process where plants make food using sunlight', textZh: '植物利用阳光制造食物的过程' },
    { key: 'B', textEn: 'A process where animals digest food', textZh: '动物消化食物的过程' },
    { key: 'C', textEn: 'A type of plant disease', textZh: '一种植物疾病' },
    { key: 'D', textEn: 'A chemical reaction in the atmosphere', textZh: '大气中的化学反应' }
  ],
  answer: 'A',
  topic: 'Science'
};

export default function TestTranslationEvent() {
  const { data: session, status } = useSession();
  const [displayLanguage, setDisplayLanguage] = useState<'en' | 'zh' | 'ms'>('en');
  const [originalText, setOriginalText] = useState('光合作用是绿色植物和一些其他生物利用阳光在叶绿素的帮助下合成食物的过程。');
  const [translatedText, setTranslatedText] = useState('Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with the help of chlorophyll.');
  
  // Toggle language
  const toggleLanguage = () => {
    setDisplayLanguage(prev => prev === 'en' ? 'zh' : prev === 'zh' ? 'ms' : 'en');
  };
  
  // Dispatch a custom translation event
  const dispatchTranslationEvent = () => {
    const event = new CustomEvent('translationComplete', {
      detail: {
        translatedText,
        originalText
      }
    });
    window.dispatchEvent(event);
  };
  
  if (status === 'loading') {
    return <div>Loading...</div>;
  }
  
  if (status === 'unauthenticated') {
    return <div>Please sign in to test the translation feature</div>;
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Head>
        <title>Test Translation Event</title>
      </Head>
      
      <main className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Translation Event Test Page</h1>
        
        <div className="mb-4">
          <button 
            onClick={toggleLanguage}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
          >
            {displayLanguage === 'en' ? 'Switch to Chinese' : 
             displayLanguage === 'zh' ? 'Switch to Malay' : 'Switch to English'}
          </button>
        </div>
        
        <QuizProvider initialQuestions={[sampleQuestion]}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="bg-white p-4 rounded-lg shadow-md flex-1">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'Test Translation Event' : 
                 displayLanguage === 'zh' ? '测试翻译事件' : 'Ujian Acara Terjemahan'}
              </h2>
              
              <div className="mb-4">
                <p className="font-bold">
                  {displayLanguage === 'en' ? 'Instructions:' : 
                   displayLanguage === 'zh' ? '说明：' : 'Arahan:'}
                </p>
                <p>
                  {displayLanguage === 'en' 
                    ? 'Fill in the text fields below and click the "Dispatch Translation Event" button to simulate a translation event.' 
                    : displayLanguage === 'zh'
                      ? '填写下面的文本字段，然后点击"发送翻译事件"按钮来模拟翻译事件。'
                      : 'Isi medan teks di bawah dan klik butang "Hantar Acara Terjemahan" untuk mensimulasikan acara terjemahan.'}
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2 font-semibold">
                  {displayLanguage === 'en' ? 'Original Text:' : 
                   displayLanguage === 'zh' ? '原文：' : 'Teks Asal:'}
                </label>
                <textarea 
                  value={originalText}
                  onChange={(e) => setOriginalText(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                />
              </div>
              
              <div className="mb-4">
                <label className="block mb-2 font-semibold">
                  {displayLanguage === 'en' ? 'Translated Text:' : 
                   displayLanguage === 'zh' ? '翻译文本：' : 'Teks Terjemahan:'}
                </label>
                <textarea 
                  value={translatedText}
                  onChange={(e) => setTranslatedText(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                />
              </div>
              
              <button 
                onClick={dispatchTranslationEvent}
                className="bg-green-500 text-white px-4 py-2 rounded"
              >
                {displayLanguage === 'en' ? 'Dispatch Translation Event' : 
                 displayLanguage === 'zh' ? '发送翻译事件' : 'Hantar Acara Terjemahan'}
              </button>
            </div>
            
            <div className="bg-white p-4 rounded-lg shadow-md md:w-2/5">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'AI Tutor Chat' : 
                 displayLanguage === 'zh' ? 'AI 导师聊天' : 'Sembang Tutor AI'}
              </h2>
              
              <AiTutorPanel 
                displayLanguage={displayLanguage === 'ms' ? 'en' : displayLanguage} 
                visible={true} 
              />
            </div>
          </div>
        </QuizProvider>
      </main>
    </div>
  );
}
