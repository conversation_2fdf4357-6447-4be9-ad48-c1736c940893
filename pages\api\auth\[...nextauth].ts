import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import prisma from '../../../lib/prisma';
import { verifyPassword, hashPin, hashPassword } from '../../../lib/auth'; // Assuming these are needed for verification
import { AuthOptions } from 'next-auth'; // Import AuthOptions type

// Define the configuration and export it
export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
        username: { label: 'Username', type: 'text' },
        pin: { label: 'PIN', type: 'password' },
      },
      async authorize(credentials: Record<string, string | undefined>, req) {
        if (!credentials) {
          return null;
        }

        const { email, password, username, pin } = credentials;

        // Parent login with email/password
        if (email && password) {
          const account = await prisma.account.findUnique({
            where: { email },
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              password_hash: true,
              salt: true,
              status: true,
            },
          });

          if (!account || account.status !== 'ACTIVE') {
            return null; // Account not found or not active
          }

          if (!account.password_hash || !account.salt) {
             console.error(`Invalid account state: Missing hash/salt for ${email}`);
             return null;
          }

          const isValid = await verifyPassword(password, account.password_hash, account.salt);
          if (!isValid) {
            return null; // Invalid password
          }

          // Return user data to be stored in the session
          return {
            id: account.id.toString(), // Ensure ID is a string
            name: account.name,
            email: account.email,
            role: account.role,
          };
        }

        // Child login with username/pin
        if (username && pin) {
          const child = await prisma.child.findUnique({
            where: { username },
            select: {
              id: true,
              name: true,
              username: true,
              pin_hash: true,
              salt: true,
              accountId: true, // Needed to check parent account status
              account: { // Include account to check status
                select: {
                  status: true,
                }
              }
            },
          });

          if (!child || !child.account || child.account.status !== 'ACTIVE') {
            return null; // Child or associated account not found or not active
          }

           if (!child.pin_hash || !child.salt) {
             console.error(`Invalid account state: Missing hash/salt for child ${username}`);
             return null;
          }

          const isValid = await verifyPassword(pin, child.pin_hash, child.salt);
          if (!isValid) {
            return null; // Invalid PIN
          }

          // Return user data to be stored in the session
          return {
            id: child.id.toString(), // Ensure ID is a string
            name: child.name,
            username: child.username,
            role: 'CHILD', // Explicitly set role for child
            parentId: child.accountId?.toString(), // Include parentId if needed
          };
        }

        return null; // Invalid credentials provided
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Add user role and other properties to the token
      if (user) {
        token.role = user.role;
        if (user.parentId) {
          token.parentId = user.parentId;
        }
      }
      return token;
    },
    async session({ session, token }) {
      // Add user role and other properties to the session
      if (token) {
        session.user.id = token.sub; // sub is the user id from authorize
        session.user.role = token.role;
        if (token.parentId) {
          session.user.parentId = token.parentId;
        }
      }
      return session;
    },
  },
  // Optional: Add pages for custom sign-in, sign-out, error
  pages: {
    signIn: '/login', // Specify the custom login page
    // signOut: '/auth/signout',
    // error: '/auth/error', // Error code passed in query string as ?error=
    // verifyRequest: '/auth/verify-request', // Used for check email page
    // newUser: '/auth/new-user' // If set, new users will be directed here on first sign in
  },
  // Optional: Configure session
  session: {
    strategy: "jwt" as const, // Use JSON Web Tokens for session management
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // Optional: Add secret for signing and encrypting tokens
  secret: process.env.NEXTAUTH_SECRET, // Use environment variable for secret
};

// Export the NextAuth handler
export default NextAuth(authOptions);
