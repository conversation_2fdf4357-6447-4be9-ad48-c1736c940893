import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]";
import { grade } from '../../lib/grading';
import { Answer, StudentAnswer } from '@prisma/client';
import { computeAndUpsertMastery } from '../../services/computeMastery';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const {
    quizAttemptId,
    questionId,
    submittedKey,
    submittedText,
    submittedJson,
    childId,
    quizType = 'mastery',
    attemptNumber = 1
  } = req.body;

  try {
    // Validate required fields
    if (!quizAttemptId || !questionId || !childId) {
      return res.status(400).json({
        message: 'Missing required fields: quizAttemptId, questionId, and childId are required'
      });
    }

    // Ensure at least one answer type is provided
    if (!submittedKey && !submittedText && !submittedJson) {
      return res.status(400).json({
        message: 'At least one of submittedKey, submittedText, or submittedJson must be provided'
      });
    }

    // Fetch the question and its answer
    const question = await prisma.question.findUnique({
      where: { id: Number(questionId) },
      include: { answer: true }
    });

    if (!question || !question.answer) {
      return res.status(404).json({ message: 'Question or answer not found' });
    }

    console.log('Question found:', {
      id: question.id,
      type: question.type,
      answer: question.answer
    });

    // Create the student answer record
    const studentAnswerData: any = {
      childId: Number(childId),
      questionId: Number(questionId),
      quizAttemptId: Number(quizAttemptId),
      quizType, // Add the quiz type
      isCorrect: false, // Default to false, will update after grading
      firstTryCorrect: false // Default to false, will update after grading if it's the first attempt
    };

    // Store the submitted answer in the database
    // The database schema has separate fields for key/text/json
    if (submittedKey !== undefined) {
      studentAnswerData.submittedKey = submittedKey;
      console.log('Setting submittedKey:', submittedKey);
    } else if (submittedText !== undefined) {
      studentAnswerData.submittedText = submittedText;
      console.log('Setting submittedText:', submittedText);
    } else if (submittedJson !== undefined) {
      studentAnswerData.submittedJson = submittedJson;
      console.log('Setting submittedJson:', JSON.stringify(submittedJson));
    } else {
      // Set a default empty value
      studentAnswerData.submittedKey = '';
      console.log('No answer provided, setting empty submittedKey');
    }

    // Create the student answer
    const studentAnswer = await prisma.studentAnswer.create({
      data: studentAnswerData
    });

    // For debugging, log the student answer data that was created
    console.log('Student answer data created:', studentAnswerData);

    // Log the answer and student answer before grading
    console.log('Before grading - Answer:', JSON.stringify(question.answer));
    console.log('Before grading - Student Answer:', JSON.stringify(studentAnswer));

    // Grade the answer
    const isCorrect = grade(question.answer as Answer, studentAnswer as StudentAnswer);

    console.log('Grading result:', isCorrect);

    // Update the student answer with the grading result
    const gradedStudentAnswer = await prisma.studentAnswer.update({
      where: { id: studentAnswer.id },
      data: {
        isCorrect,
        firstTryCorrect: attemptNumber === 1 && isCorrect // Only set to true if it's the first attempt and correct
      }
    });

    // Update mastery levels if feature flag is enabled
    if (process.env.FEATURE_ADAPTIVE_V2 === 'on') {
      await computeAndUpsertMastery(childId, 'unit',    question.unitId);
      await computeAndUpsertMastery(childId, 'subject', question.subjectId);
    }

    // Get the current quiz attempt
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: { id: Number(quizAttemptId) },
      include: { studentAnswers: true }
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    // Check if this was the last question
    const isLastQuestion = quizAttempt.currentQuestionIndex === quizAttempt.questionIds.length - 1;

    console.log('Quiz attempt details:', {
      currentQuestionIndex: quizAttempt.currentQuestionIndex,
      totalQuestions: quizAttempt.questionIds.length,
      isLastQuestion,
      status: quizAttempt.status
    });

    // Calculate score if this is the last question
    let score: number | undefined = undefined;

    if (isLastQuestion) {
      // Include the current answer in the score calculation
      const correctAnswers = quizAttempt.studentAnswers.filter(sa => sa.isCorrect).length + (isCorrect ? 1 : 0);
      score = (correctAnswers / quizAttempt.questionIds.length) * 100;
      console.log('Calculated final score:', score, 'based on', correctAnswers, 'correct answers out of', quizAttempt.questionIds.length);
    }

    // Update the quiz attempt
    const updatedQuizAttempt = await prisma.quizAttempt.update({
      where: { id: Number(quizAttemptId) },
      data: {
        // Increment the current question index if not the last question
        currentQuestionIndex: isLastQuestion
          ? quizAttempt.currentQuestionIndex
          : quizAttempt.currentQuestionIndex + 1,
        // Set end time and status if this is the last question
        ...(isLastQuestion && {
          endTime: new Date(),
          status: 'COMPLETED',
          score
        })
      }
    });

    // Return the result
    res.status(200).json({
      correct: isCorrect,
      studentAnswer: gradedStudentAnswer,
      quizAttempt: updatedQuizAttempt,
      isLastQuestion,
      score: isLastQuestion ? score : undefined
    });

  } catch (error) {
    console.error('Error submitting answer:', error);
    res.status(500).json({ message: 'Error submitting answer' });
  }
}
