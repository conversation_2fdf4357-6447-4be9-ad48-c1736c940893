import { z } from 'zod';
import { QuestionType } from '@prisma/client';
import { QuestionSpecMap } from '../../types/question';

// Schema for fill-in-the-blank questions
export const fillBlankSpecSchema = z.object({
  template: z.string().min(1, "Template is required"),
  blanks: z.array(z.object({
    correct: z.string().min(1, "Correct answer is required"),
    distractors: z.array(z.string())
  })).min(1, "At least one blank is required"),
  caseSensitive: z.boolean().optional()
});

// Schema for matching questions
export const matchingSpecSchema = z.object({
  pairs: z.array(z.object({
    left: z.string().min(1, "Left item is required"),
    leftZh: z.string().optional(),
    leftMs: z.string().optional(),
    right: z.string().optional(),
    rightZh: z.string().optional(),
    rightMs: z.string().optional(),
    rightImage: z.string().url("Right image must be a valid URL").optional()
  })).min(2, "At least two pairs are required"),
  shuffleLeft: z.boolean().optional(),
  shuffleRight: z.boolean().optional()
});

// Schema for sequencing questions
export const sequencingSpecSchema = z.object({
  items: z.array(
    z.union([
      z.string(),
      z.object({
        textEn: z.string(),
        textZh: z.string().optional(),
        textMs: z.string().optional()
      })
    ])
  ).min(2, "At least two items are required"),
  correctOrder: z.array(z.number().int().nonnegative()),
  allowPartialCredit: z.boolean().optional()
}).refine(
  (data) => data.items.length === data.correctOrder.length,
  { message: "Items and correctOrder must have the same length" }
).refine(
  (data) => {
    const sorted = [...data.correctOrder].sort((a, b) => a - b);
    return sorted.every((val, idx) => val === idx);
  },
  { message: "correctOrder must be a permutation of [0, 1, 2, ...]" }
);

// Schema for long answer questions
export const longAnswerSpecSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  keyPoints: z.array(z.string()).min(1, "At least one key point is required"),
  minSimilarity: z.number().min(0).max(1, "Similarity must be between 0 and 1"),
  maxWords: z.number().int().positive().optional()
});

// Schema for multiple choice image questions
export const multipleChoiceImageSpecSchema = z.object({
  shuffleChoices: z.boolean().optional(),
  choiceImageStyle: z.enum(['square', 'circle', 'thumbnail']).optional()
});

// Schema for picture prompt questions
export const picturePromptSpecSchema = z.object({
  altTextEn: z.string().optional(),
  altTextZh: z.string().optional(),
  license: z.string().optional()
});

// Schema for true/false questions
export const trueFalseSpecSchema = z.object({
  statement: z.string().min(1, "Statement is required"),
  correctAnswer: z.boolean()
});

// Schema for short answer questions
export const shortAnswerSpecSchema = z.object({
  acceptableAnswers: z.array(z.string()).min(1, "At least one acceptable answer is required"),
  maxChars: z.number().int().positive().optional(),
  caseSensitive: z.boolean().optional()
});

// Map of question types to their schema
const questionSpecSchemas = {
  MULTIPLE_CHOICE: z.undefined(),
  MULTIPLE_CHOICE_IMAGE: multipleChoiceImageSpecSchema,
  PICTURE_PROMPT: picturePromptSpecSchema,
  FILL_IN_THE_BLANK: fillBlankSpecSchema,
  TRUE_FALSE: trueFalseSpecSchema,
  SHORT_ANSWER: shortAnswerSpecSchema,
  LONG_ANSWER: longAnswerSpecSchema,
  MATCHING: matchingSpecSchema,
  SEQUENCING: sequencingSpecSchema
} as const;

/**
 * Validates a question spec based on the question type
 * @param type The question type
 * @param spec The spec to validate
 * @returns The validated spec or throws an error
 */
export function validateSpec<T extends QuestionType>(
  type: T,
  spec: unknown
): QuestionSpecMap[T] {
  if (!(type in questionSpecSchemas)) {
    throw new Error(`Unknown question type: ${type}`);
  }

  const schema = questionSpecSchemas[type as keyof typeof questionSpecSchemas];
  return schema.parse(spec) as QuestionSpecMap[T];
}

/**
 * Safely validates a question spec, returning a result object instead of throwing
 * @param type The question type
 * @param spec The spec to validate
 * @returns An object with success flag and either the validated data or an error
 */
export function safeValidateSpec<T extends QuestionType>(
  type: T,
  spec: unknown
): { success: true; data: QuestionSpecMap[T] } | { success: false; error: z.ZodError } {
  try {
    const validatedSpec = validateSpec(type, spec);
    return { success: true, data: validatedSpec };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
}
