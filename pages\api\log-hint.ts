import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import prisma from '../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  // For testing purposes, we'll allow unauthenticated requests
  // if (!session) {
  //   return res.status(401).json({ message: 'Unauthorized' });
  // }

  try {
    const { attemptId, questionId, stage } = JSON.parse(req.body);

    if (!questionId) {
      return res.status(400).json({ message: 'Question ID is required' });
    }

    // Log the hint usage
    await prisma.hintLog.create({
      data: {
        questionId,
        childId: session?.user?.id ? parseInt(session.user.id as string, 10) : 1, // Use default childId if not authenticated
        hint: `Hint stage ${stage} requested for question ${questionId} in attempt ${attemptId}`,
      },
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error logging hint usage:', error);
    return res.status(500).json({ message: 'Error logging hint usage', error });
  }
}
