import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const questionData = req.body;

  // Basic validation
  if (!questionData || !questionData.type || !questionData.year || !questionData.subject || !questionData.unit || !questionData.prompt) {
    return res.status(400).json({ message: 'Missing required question data' });
  }

  try {
    // Generate a unique questionId (simple timestamp-based for now)
    const questionId = `q${Date.now()}`;

    // Find or create related Subject, Year, and Unit
    const yearRecord = await prisma.year.upsert({
      where: { yearNumber: questionData.year },
      update: {},
      create: { yearNumber: questionData.year },
    });

    const subjectRecord = await prisma.subject.upsert({
      where: { name: questionData.subject },
      update: {},
      create: { name: questionData.subject },
    });

    const unitRecord = await prisma.unit.upsert({
      where: {
        unitNumber_subjectId_yearId: {
          unitNumber: questionData.unit,
          subjectId: subjectRecord.id,
          yearId: yearRecord.id,
        },
      },
      update: {},
      create: {
        unitNumber: questionData.unit,
        topicEn: questionData.topic || '', // Use topic from JSON or empty string
        topicZh: '', // Assuming Chinese topic is not required initially
        subject: { connect: { id: subjectRecord.id } },
        year: { connect: { id: yearRecord.id } },
      },
    });

    // Create the Question record
    const newQuestion = await prisma.question.create({
      data: {
        questionId: questionId,
        type: questionData.type.toUpperCase(), // Ensure type is uppercase for enum
        promptEn: questionData.prompt.en || '',
        promptZh: questionData.prompt.zh || '',
        promptMs: questionData.prompt.ms || '',
        originalLanguage: questionData.originalLanguage || 'ZH', // Default to Chinese if not specified
        translationState: questionData.translationState || 'PARTIAL', // Default to PARTIAL if not specified
        topic: questionData.topic || '',
        subject: { connect: { id: subjectRecord.id } },
        year: { connect: { id: yearRecord.id } },
        unit: { connect: { id: unitRecord.id } },
        explanation: questionData.explanation ? {
          create: {
            textEn: questionData.explanation.en || '',
            textZh: questionData.explanation.zh || '',
            textMs: questionData.explanation.ms || '',
          },
        } : undefined,
        choices: questionData.choices ? {
          create: questionData.choices.map((choice: any) => ({
            key: choice.key,
            textEn: choice.en || '',
            textZh: choice.zh || '',
          })),
        } : undefined,
        answer: (questionData.type.toUpperCase() === 'MULTIPLE_CHOICE' && (questionData.answer?.key || (questionData.answers && questionData.answers.length > 0))) ? {
           create: {
             key: questionData.answer?.key || questionData.answers[0], // Use key from answer object or first element of answers array
             textEn: questionData.answer?.en || '', // Include text if available (for consistency, though not strictly needed for MC answer key)
             textZh: questionData.answer?.zh || '', // Include text if available
           }
        } : (questionData.type.toUpperCase() === 'SHORT_ANSWER' && questionData.answer?.en) ? {
           create: {
             key: null, // Short answer doesn't use a key
             textEn: questionData.answer.en,
             textZh: questionData.answer.zh || '',
           }
        } : undefined, // Don't create answer if no valid data for either type
      },
      include: {
        choices: true,
        answer: true,
        explanation: true,
      },
    });

    res.status(201).json(newQuestion);
  } catch (error) {
    console.error('Error adding question:', error);
    // Return a more informative error if it's a Prisma validation error
    if (error.code === 'P2002') {
       res.status(400).json({ message: `Unique constraint failed for ${error.meta.target}`, error: error.message });
    } else if (error.code === 'P2003') {
       res.status(400).json({ message: `Foreign key constraint failed on field: ${error.meta.field_name}`, error: error.message });
    }
    else {
      res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
  }
}
