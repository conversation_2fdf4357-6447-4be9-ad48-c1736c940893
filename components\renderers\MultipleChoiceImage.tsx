import React from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';
import { Choice } from './MultipleChoice';
import { useQuiz } from '../quiz/QuizContext';

interface MultipleChoiceImageProps extends BaseRendererProps<'MULTIPLE_CHOICE_IMAGE'> {
  choices: (Choice & { mediaUrl?: string; mediaAlt?: string })[];
}

const MultipleChoiceImage: React.FC<MultipleChoiceImageProps> = ({
  promptEn,
  promptZh,
  originalPrompt,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  choices,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp,
  isIncorrect,
  isCorrect,
  submittedAnswer
}) => {
  // Get the handleNext function from the QuizContext
  const { handleNext } = useQuiz();

  // Get the original language from the parent component
  // This is passed from Quiz.tsx via the originalPrompt prop
  const isChineseOriginal = originalPrompt === promptZh;
  const isMalayOriginal = originalPrompt !== promptZh && originalPrompt !== promptEn;
  const handleSelect = (key: string) => {
    onAnswerChange(key);
  };

  // Get image style from spec
  const imageStyle = spec?.choiceImageStyle || 'square';

  // Apply appropriate CSS classes based on image style
  const getImageClasses = () => {
    switch (imageStyle) {
      case 'circle':
        return 'w-16 h-16 rounded-full object-cover';
      case 'thumbnail':
        return 'w-20 h-16 object-cover rounded';
      case 'square':
      default:
        return 'w-16 h-16 object-cover rounded';
    }
  };

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="space-y-3">
        {choices.map(choice => {
          const isSelected = selectedAnswer === choice.key;

          // Determine background color based on selection and correctness
          let bg = 'bg-white text-black';
          if (isSelected) {
            // Only show red for incorrect answers if this is the submitted answer
            if (isIncorrect && selectedAnswer === submittedAnswer) {
              // If answer is selected, submitted, and incorrect, show red
              bg = 'bg-red-300';
            } else {
              // If answer is selected but not submitted or not incorrect, show blue
              bg = 'bg-blue-300';
            }
          }

          return (
            <div key={choice.key} className="choice-container">
              <button
                onClick={() => handleSelect(choice.key)}
                className={`${bg} w-full text-left rounded px-4 py-3 flex items-center justify-between focus:outline-none`}
              >
                <div className="flex items-center">
                  {choice.mediaUrl && (
                    <img
                      src={choice.mediaUrl}
                      alt={choice.mediaAlt || ''}
                      className={`${getImageClasses()} mr-3`}
                    />
                  )}
                  <span
                    onMouseUp={(e) => {
                      const selection = window.getSelection()?.toString().trim();
                      if (selection) {
                        e.stopPropagation();
                        if (onMouseUp) onMouseUp(e);
                      } else {
                        handleSelect(choice.key);
                      }
                    }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    {isChineseOriginal
                      ? choice.textZh
                      : (isMalayOriginal && choice.textMs)
                        ? choice.textMs
                        : choice.textEn}
                  </span>
                </div>
                {isSelected && isIncorrect && selectedAnswer === submittedAnswer && (
                  <div className="text-red-700 font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    {displayLanguage === 'en' ? 'Incorrect' : displayLanguage === 'ms' ? 'Tidak betul' : '不正确'}
                  </div>
                )}
                {isSelected && isCorrect && selectedAnswer === submittedAnswer && (
                  <div className="text-green-700 font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {displayLanguage === 'en' ? 'Correct!' : displayLanguage === 'ms' ? 'Betul!' : '正确！'}
                  </div>
                )}
              </button>
            </div>
          );
        })}

      </div>
    </RendererWrapper>
  );
};

export default MultipleChoiceImage;
