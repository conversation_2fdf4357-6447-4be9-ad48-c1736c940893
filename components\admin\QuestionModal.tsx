import React, { useEffect, useState } from 'react';
import use<PERSON><PERSON> from 'swr';
import {
  MultipleC<PERSON>ice,
  MultipleChoiceImage,
  ShortAnswer,
  FillInTheBlank,
  TrueFalse,
  Matching,
  Sequencing,
  LongAnswer,
  PicturePrompt
} from '../renderers';
import ReactMarkdown from 'react-markdown';
import { QuizProvider } from '../quiz/QuizContext';
import { QuizV2Provider, QuestionPanel } from '../quizV2';

// Badge component for the right panel
function Badge({ children }: { children: React.ReactNode }) {
  return (
    <span className="inline-block bg-yellow-200 text-yellow-800 px-2 py-0.5 rounded text-xs font-medium">
      {children}
    </span>
  );
}

interface QuestionModalProps {
  id: number;
  onClose: () => void;
}

const fetcher = (url: string) => fetch(url).then((res) => res.json());

const QuestionModal: React.FC<QuestionModalProps> = ({ id, onClose }) => {
  // Fetch question data
  const { data: question, error, isLoading } = useSWR(`/api/admin/question/${id}`, fetcher);

  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscapeKey);

    return () => {
      window.removeEventListener('keydown', handleEscapeKey);
    };
  }, [onClose]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg w-[90vw] h-[90vh] flex items-center justify-center">
          <div className="text-xl">Loading question...</div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error || !question) {
    return (
      <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg w-[90vw] h-[90vh] flex items-center justify-center">
          <div className="text-xl text-red-600">Error loading question</div>
        </div>
      </div>
    );
  }

  // Render question using QuizV2 components to match what students see
  const renderQuestion = () => {
    // Create a mock question for the QuizV2Provider
    const mockQuestion = {
      id: question.id,
      questionId: question.questionId,
      type: question.type,
      promptEn: question.promptEn,
      promptZh: question.promptZh,
      promptMs: question.promptMs,
      originalLanguage: question.originalLanguage || 'EN',
      choices: question.choices || [],
      spec: question.spec || {},
      keywords: question.keywords || null,
      explanation: question.explanation || null,
      answer: question.answer || null,
      unit: question.unit || null,
      subject: question.subject || null
    };

    // Create a mock attempt object for the QuizV2Provider
    const mockAttempt = {
      questions: [mockQuestion],
      quizAttempt: {
        id: 0,
        questionIds: [question.id.toString()],
        currentQuestionIndex: 0,
        quizType: 'MASTERY',
        metadata: {
          configSnapshot: {
            allowTranslate: true,
            allowHints: true,
            allowAiTutor: true,
            reviewMissedQuestions: true
          }
        }
      },
      isCompleted: false
    };

    // Use the QuizV2 components for preview
    return (
      <div className="w-full max-w-2xl mx-auto">
        <QuizV2Provider attempt={mockAttempt}>
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <QuestionPanel />
          </div>
        </QuizV2Provider>
      </div>
    );
  };

  // Render answer based on question type
  const renderAnswer = () => {
    if (!question.answer) return <div>No answer available</div>;

    // For multiple choice questions
    if (question.type === 'MULTIPLE_CHOICE' || question.type === 'MULTIPLE_CHOICE_IMAGE') {
      const correctChoice = question.choices?.find(c => c.key === question.answer.key);
      return (
        <div>
          <div className="font-medium">{question.answer.key}</div>
          {correctChoice && (
            <div>
              {/* Always show English answer first */}
              {correctChoice.textEn && (
                <div className="mt-1">
                  <span className="text-xs text-gray-500">EN: </span>
                  {correctChoice.textEn}
                </div>
              )}
              {/* Show Chinese answer if available */}
              {correctChoice.textZh && (
                <div className="mt-1">
                  <span className="text-xs text-gray-500">ZH: </span>
                  {correctChoice.textZh}
                </div>
              )}
            </div>
          )}
        </div>
      );
    }

    // For TRUE_FALSE questions
    if (question.type === 'TRUE_FALSE') {
      // TRUE_FALSE answers are stored in the key field
      const answerValue = question.answer.key?.toUpperCase();
      return (
        <div>
          <div className="font-medium">
            {answerValue === 'TRUE' ? 'True / 正确' : answerValue === 'FALSE' ? 'False / 错误' : answerValue}
          </div>
        </div>
      );
    }

    // For text-based answers
    if (question.answer.textEn || question.answer.textZh) {
      return (
        <div>
          {/* Always show English answer first */}
          {question.answer.textEn && (
            <div className="mb-1">
              <span className="text-xs text-gray-500">EN: </span>
              {question.answer.textEn}
            </div>
          )}
          {/* Show Chinese answer if available */}
          {question.answer.textZh && (
            <div>
              <span className="text-xs text-gray-500">ZH: </span>
              {question.answer.textZh}
            </div>
          )}
        </div>
      );
    }

    // For complex answers (stored in answerSpec)
    if (question.answer.answerSpec) {
      return <pre className="text-xs overflow-auto max-h-32">{JSON.stringify(question.answer.answerSpec, null, 2)}</pre>;
    }

    return <div>Answer format not supported</div>;
  };

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50" onClick={onClose}>
      <div
        className="bg-white rounded-lg w-[90vw] h-[90vh] flex overflow-hidden"
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside from closing
      >
        {/* Left side - Question preview */}
        <div className="flex-1 overflow-auto p-6 bg-blue-50">
          <div className="mb-4 flex justify-between items-center">
            <h2 className="text-xl font-bold">Question Preview (V2 Interface)</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {renderQuestion()}
        </div>

        {/* Right side - Answer & explanation */}
        <div className="w-72 p-4 border-l text-sm overflow-auto">
          <div className="mb-6">
            <div className="text-xs text-gray-500 mb-1">Question ID: {question.questionId}</div>
            <div className="text-xs text-gray-500 mb-1">Type: {question.type}</div>
            <div className="text-xs text-gray-500 mb-1">
              Subject: {question.subject?.name}, Year: {question.year?.yearNumber}
            </div>
            {question.unit && (
              <div className="text-xs text-gray-500 mb-1">
                Unit: {question.unit.unitNumber} - {question.unit.topicEn}
              </div>
            )}
          </div>

          <div className="mb-4">
            <Badge>Question</Badge>
            <div className="mt-2 bg-gray-50 p-2 rounded">
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown>
                  {question.promptEn}
                </ReactMarkdown>
              </div>
            </div>
          </div>

          <div className="mb-4">
            <Badge>Answer</Badge>
            <div className="mt-2 bg-gray-50 p-2 rounded">
              {renderAnswer()}
            </div>
          </div>

          {question.explanation && (
            <div>
              <Badge>Explanation</Badge>
              <div className="mt-2 bg-gray-50 p-2 rounded">
                {/* Always show English explanation */}
                {question.explanation.textEn && (
                  <div className="prose prose-sm max-w-none mb-3">
                    <div className="font-medium text-xs text-gray-500 mb-1">EN:</div>
                    <ReactMarkdown>
                      {question.explanation.textEn}
                    </ReactMarkdown>
                  </div>
                )}
                {/* Show Chinese explanation if available */}
                {question.explanation.textZh && (
                  <div className="prose prose-sm max-w-none border-t pt-2">
                    <div className="font-medium text-xs text-gray-500 mb-1">ZH:</div>
                    <ReactMarkdown>
                      {question.explanation.textZh}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionModal;
