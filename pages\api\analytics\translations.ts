import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { childId, timePeriod } = req.query; // childId and timePeriod are optional query parameters

  try {
    let whereClause: any = {};
    if (childId) {
      try {
        whereClause.childId = parseInt(childId as string, 10);
        if (isNaN(whereClause.childId)) {
          return res.status(400).json({ message: 'Invalid childId parameter' });
        }
      } catch (parseError) {
        return res.status(400).json({ message: 'Invalid childId parameter' });
      }
    }

    // Basic filtering by time period (can be expanded)
    if (timePeriod) {
      const now = new Date();
      let startDate: Date | undefined;
      switch (timePeriod) {
        case 'day':
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          break;
      }
      if (startDate) {
        whereClause.timestamp = {
          gte: startDate,
        };
      }
    }

    // Wrap database query in try-catch to handle specific database errors
    let translations: any[] = [];
    try {
      translations = await prisma.translationLog.findMany({
        where: whereClause,
        select: {
          translatedText: true,
          child: {
            select: {
              name: true,
            },
          },
          question: {
            select: {
              id: true,
              promptEn: true,
              promptZh: true,
            },
          },
          timestamp: true,
        },
      });
    } catch (dbError) {
      console.error('Database error fetching translations:', dbError);
      return res.status(500).json({
        message: 'Database error fetching translations',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      });
    }

    // Aggregate data (basic example: count translations and find common words)
    const translationCount = translations.length;
    const wordFrequency: { [key: string]: number } = {};

    translations.forEach((log: any) => {
      // Skip logs with missing translatedText
      if (!log.translatedText) {
        console.warn('Translation log missing translatedText');
        return;
      }

      try {
        const words = log.translatedText.toLowerCase().split(/\s+/);
        words.forEach((word: string) => {
          if (word) {
            wordFrequency[word] = (wordFrequency[word] || 0) + 1;
          }
        });
      } catch (parseError) {
        console.warn('Error parsing translation text:', parseError);
      }
    });

    // Sort common words by frequency
    const commonWords = Object.entries(wordFrequency)
      .sort(([, a], [, b]) => b - a)
      .map(([word, count]) => ({ word, count }));

    res.status(200).json({
      translationCount,
      commonWords,
      // Exclude raw translations data to reduce payload size
      // translations,
    });

  } catch (error) {
    console.error('Error fetching translation analytics:', error);
    res.status(500).json({
      message: 'Error fetching translation analytics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Error disconnecting from database:', disconnectError);
    }
  }
}
