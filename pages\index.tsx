// Test component to switch between roles
import { useState, useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'; // Import useRouter
import Dashboard from '../components/Dashboard'
import ParentPortal from '../components/ParentPortal'
import { useSession, signIn, signOut } from 'next-auth/react'
import { Role } from '@prisma/client'

// Test component to switch between roles
function RoleSwitcher({ onSwitch }: { onSwitch: (role: Role) => void }) {
  return (
    <div className="fixed top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-sm font-bold mb-2">Test Mode: Switch Role</h3>
      <div className="space-x-2">
        <button
          onClick={() => onSwitch(Role.CHILD)}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Student View
        </button>
        <button
          onClick={() => onSwitch(Role.PARENT)}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Parent View
        </button>
      </div>
    </div>
  );
}

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter(); // Initialize useRouter

  // Function to switch roles for testing
  const switchRole = async (role: Role) => {
    // Sign out current session
    await signOut({ redirect: false });

    // Mock credentials based on role
    const credentials = role === Role.CHILD
      ? { username: 'johndoe', pin: '1234' }  // Mock child credentials
      : { email: '<EMAIL>', password: 'password123' };  // Mock parent credentials

    // Sign in with new role
    await signIn('credentials', {
      ...credentials,
      redirect: false
    });
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  // If not authenticated, redirect to login
  if (status === 'unauthenticated') {
    router.push('/login');
    return <div>Redirecting to login...</div>;
  }

  const user = session?.user;

  return (
    <>
      {/*<RoleSwitcher onSwitch={switchRole} />}*/}
      {user?.role === Role.CHILD ? (
        router.push('/student-dashboard')
      ) : user?.role === Role.PARENT ? (
        <ParentPortal onAssignHomework={() => {/* Implement homework assignment */}} />
      ) : (
        <div>Access Denied</div>
      )}
    </>
  );
}
