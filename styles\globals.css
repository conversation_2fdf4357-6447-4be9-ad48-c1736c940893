@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  padding: 0;
  margin: 0;
  font-family: 'Noto Sans SC', 'Chinese Fallback', 'Microsoft YaHei', 'SimSun', 'STHeiti', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Add specific styles for Chinese text */
.chinese-text {
  font-family: 'Noto Sans SC', 'Chinese Fallback', 'Microsoft YaHei', 'SimSun', 'STHeiti', sans-serif;
  font-weight: 700;
}

/* Add this to ensure text inside buttons can be highlighted */
button span {
  user-select: text;
}

/* Styles for highlighted keywords */
.keyword-highlight {
  background-color: rgba(255, 255, 0, 0.5);
  border-radius: 2px;
  padding: 0 2px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    background-color: rgba(255, 255, 0, 0.3);
  }
  50% {
    background-color: rgba(255, 255, 0, 0.7);
  }
  100% {
    background-color: rgba(255, 255, 0, 0.3);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.animate-slideUp {
  animation: slideUp 0.3s ease-out forwards;
}
