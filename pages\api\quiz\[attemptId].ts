import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
// import { getSession } from 'next-auth/react'; // Use getServerSession instead
import { getServerSession } from 'next-auth/next'; // Import recommended server-side utility
import { authOptions } from '../auth/[...nextauth]'; // Import your authOptions
import { shuffle } from 'lodash';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Use getServerSession for server-side session retrieval
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { attemptId } = req.query;

  if (!attemptId || Array.isArray(attemptId)) {
    return res.status(400).json({ message: 'Invalid attemptId' });
  }

  try {
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: {
        id: Number(attemptId),
      },
      include: {
        // Use include instead of select to get all fields
        studentAnswers: true,
        child: {
          select: {
            quizLanguage: true,
            menuLanguage: true,
            showDualLanguage: true
          }
        }
      },
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    // Verify that the logged-in user is the child who owns the quiz attempt
    // Convert both IDs to strings for proper comparison
    const sessionUserId = session.user?.id;
    const quizAttemptChildId = quizAttempt.childId.toString();

    if (!sessionUserId || sessionUserId !== quizAttemptChildId) {
      console.log('Access denied - User ID mismatch', { sessionUserId, quizAttemptChildId });
      return res.status(403).json({ 
        message: 'Access denied - You can only view your own quizzes'
      });
    }

    // Check if the quiz is already completed
    if (quizAttempt.status === 'COMPLETED') {
      // Return a response indicating that the quiz is already completed
      return res.status(200).json({
        quizAttempt,
        isCompleted: true,
        message: 'This quiz has already been completed.'
      });
    }


    // Fetch the actual question data based on the stored questionIds
    const questions = await prisma.question.findMany({ // Corrected model name
      where: {
        id: {
          in: quizAttempt.questionIds.map(id => Number(id)),
        },
      },
      include: {
        choices: true,
        answer: true, // Include the actual answer
        explanation: true, // Include the explanation
        unit: {
          select: {
            unitNumber: true,
            topicEn: true,
            topicZh: true,
          }
        },
        subject: {
          select: {
            name: true,
          }
        },
        year: {
          select: {
            yearNumber: true,
          }
        }
      },
    });

    console.log('Found questions:', questions.length);

    // Get or initialize the shuffled choices map from metadata
    let shuffledChoicesMap = quizAttempt.metadata?.shuffledChoices || {};
    let needsMetadataUpdate = false;

    // Order the questions based on the order in questionIds and transform structure
    const transformedQuestions = quizAttempt.questionIds // Assign result to transformedQuestions
      .map(id => questions.find(q => q.id === Number(id)))
      .filter(q => q !== undefined) // Filter out any questions not found (shouldn't happen if questionIds are valid)
      .map(q => {
        // Create answer structure from the actual answer
        let answerData: any;
        if (q.answer) {
          // Use type assertion to handle the Answer type
          const answer = q.answer as any;

          console.log('Processing answer for question:', q.id, 'Answer:', JSON.stringify(answer));

          // Use the actual answer data
          if (answer.type === 'SINGLE_CHOICE') {
            // For single choice, use the key
            answerData = answer.key;
            console.log('SINGLE_CHOICE answer key:', answer.key);
          } else if (answer.type === 'SHORT_TEXT') {
            // For short text, use the text fields
            answerData = {
              en: answer.textEn || '',
              zh: answer.textZh || '',
              ms: answer.textMs || ''
            };
            console.log('SHORT_TEXT answer:', answerData);
          } else if (answer.answerSpec) {
            // For complex answers, use the answerSpec
            answerData = answer.answerSpec;
            console.log('Complex answer with answerSpec:', answer.answerSpec);
          } else {
            // Fallback
            answerData = { key: answer.key };
            console.log('Fallback answer with key:', answer.key);
          }
        } else {
          // Fallback if no answer is found
          answerData = { en: 'No answer available', zh: '没有可用的答案' };
          console.log('No answer found for question:', q.id);
        }

        // Create explanation structure from the actual explanation
        const explanationData = q.explanation ? {
          en: (q.explanation as any).textEn || 'No explanation available',
          zh: (q.explanation as any).textZh || '没有可用的解释',
          ms: (q.explanation as any).textMs || ''
        } : {
          en: 'No explanation available',
          zh: '没有可用的解释'
        };

        // Handle shuffling for multiple choice questions
        let choices = q.choices;
        const questionIdStr = q.id.toString();

        // Check if this is a multiple choice question
        if ((q.type === 'MULTIPLE_CHOICE' || q.type === 'MULTIPLE_CHOICE_IMAGE') && choices && choices.length > 0) {
          // If we don't have shuffled choices for this question yet, create them
          if (!shuffledChoicesMap[questionIdStr]) {
            console.log(`Shuffling choices for question ${questionIdStr}`);
            // Shuffle the choices and store the order
            const shuffledChoices = shuffle([...choices]);
            shuffledChoicesMap[questionIdStr] = shuffledChoices.map(c => c.id);
            needsMetadataUpdate = true;
            choices = shuffledChoices;
          } else {
            console.log(`Using existing shuffled order for question ${questionIdStr}`);
            // Use the existing shuffled order
            const choiceOrder = shuffledChoicesMap[questionIdStr];
            // Sort the choices based on the stored order
            choices = choiceOrder.map(id => choices.find(c => c.id === id)).filter(Boolean);
          }
        }

        // Transform the question structure to match frontend expectations
        const transformedQuestion = {
          ...q,
          // Keep the original fields for debugging
          promptEn: q.promptEn,
          promptZh: q.promptZh,
          originalLanguage: (q as any).originalLanguage || 'EN', // Ensure originalLanguage is included, default to EN
          prompt: { en: q.promptEn, zh: q.promptZh },
          // Use the actual answer and explanation
          answer: answerData,
          explanation: explanationData,
          topic: q.subTopicEn || (q.unit ? q.unit.topicEn : ''),
          // Use the shuffled choices if applicable
          choices: choices
        };

        // Log the transformed question for debugging
        console.log('Transformed question type:', transformedQuestion.type);
        console.log('Transformed question has choices:', transformedQuestion.choices ? transformedQuestion.choices.length : 0);
        console.log('Transformed question answer:', transformedQuestion.answer);

        return transformedQuestion;
      });

    // Update the metadata if we shuffled any choices
    if (needsMetadataUpdate) {
      console.log('Updating quiz attempt metadata with shuffled choices');
      await prisma.quizAttempt.update({
        where: { id: quizAttempt.id },
        data: {
          metadata: {
            ...(quizAttempt.metadata || {}),
            shuffledChoices: shuffledChoicesMap
          }
        }
      });
    }


    // Include the child's language preferences in the response
    const childLanguagePreferences = {
      quizLanguage: quizAttempt.child?.quizLanguage || 'ZH', // Default to ZH if not set
      menuLanguage: quizAttempt.child?.menuLanguage || 'EN',  // Default to EN if not set
      showDualLanguage: quizAttempt.child?.showDualLanguage || false // Default to false if not set
    };

    res.status(200).json({
      quizAttempt,
      questions: transformedQuestions,
      childLanguagePreferences
    });

  } catch (error) {
    console.error('Error fetching quiz attempt:', error);
    res.status(500).json({ message: 'Error fetching quiz attempt' });
  }
}
