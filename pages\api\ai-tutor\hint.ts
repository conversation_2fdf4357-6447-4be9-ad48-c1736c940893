import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  // For testing purposes, we'll allow unauthenticated requests
  // if (!session) {
  //   return res.status(401).json({ message: 'Unauthorized' });
  // }

  // Get the question ID from the request body
  const { questionId } = req.body;

  if (!questionId) {
    return res.status(400).json({ message: 'Question ID is required' });
  }

  try {
    // Fetch the question from the database
    const question = await prisma.question.findUnique({
      where: { id: questionId },
      include: {
        keywords: true,
        choices: true,
      },
    });

    if (!question) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Check for OpenRouter API key
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    if (!openRouterApiKey) {
      return res.status(500).json({ message: 'OpenRouter API key not configured' });
    }

    // Prepare the prompt for the AI
    const prompt = `
    I need a hint for the following question:

    Question: ${question.promptEn || question.promptZh}

    Please provide a helpful hint that guides the student towards the answer without giving it away directly.
    The hint should be concise (1-2 sentences) and appropriate for elementary school students.
    `;

    // Query OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [
          { role: 'system', content: 'You are a helpful tutor providing hints for quiz questions.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return res.status(500).json({
        message: 'LLM query failed',
        error: errorData.error?.message || 'Unknown error'
      });
    }

    const data = await response.json();
    const hint = data.choices?.[0]?.message?.content || 'No hint available';

    // Log the hint request
    try {
      await prisma.hintLog.create({
        data: {
          questionId,
          childId: session?.user?.id ? parseInt(session.user.id as string, 10) : 1, // Use default childId if not authenticated
          hint,
        },
      });
    } catch (logError) {
      console.error('Error logging hint:', logError);
      // Continue even if logging fails
    }

    return res.status(200).json({ hint });
  } catch (error) {
    console.error('Error generating hint:', error);
    return res.status(500).json({ message: 'Error generating hint', error });
  }
}
