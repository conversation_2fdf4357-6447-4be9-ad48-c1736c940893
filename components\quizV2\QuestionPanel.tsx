import React from 'react';
import { useQuizV2 } from './QuizV2Provider';
import { Language } from '@prisma/client';
import HighlightedText from '../renderers/HighlightedText';

export default function QuestionPanel() {  const {
    questions,
    currentIndex,
    selectedAnswer,
    setSelectedAnswer,
    getQuestionContent,
    getChoiceContent,
    getSecondaryQuestionContent,
    getSecondaryChoiceContent,
    showDualLanguage,
    childQuizLanguage,
    childMenuLanguage,
    onMouseUp,
    isAnswerChecked,
    isAnswerCorrect,
    showKeywords
  } = useQuizV2();

  // Get the current question
  const currentQuestion = questions[currentIndex];

  if (!currentQuestion) {
    return (
      <div className="p-5">
        <h2 className="text-xl font-bold mb-4 text-blue-700">
          Question {currentIndex + 1}
        </h2>
        <div className="bg-white rounded-lg p-4">
          <p className="text-gray-800 text-lg">Question not available</p>
        </div>
      </div>
    );
  }

  // Get the question text using the getQuestionContent helper
  // This uses the child's quiz language preference, not the display language
  const getQuestionText = () => {
    const primaryText = getQuestionContent(currentQuestion);

    // If dual language is enabled, add the secondary text
    if (showDualLanguage && childQuizLanguage !== childMenuLanguage) {
      const secondaryText = getSecondaryQuestionContent(currentQuestion);

      // Only show dual language if the texts are different
      if (primaryText !== secondaryText) {
        return (
          <>
            <HighlightedText
              text={primaryText}
              keywords={currentQuestion.keywords}
              language={childQuizLanguage}
              showHighlights={showKeywords}
              className="mb-2"
            />
            <HighlightedText 
              text={secondaryText}
              keywords={currentQuestion.keywords}
              language={childMenuLanguage}
              showHighlights={showKeywords}
              className="text-gray-500 italic"
            />
          </>
        );
      }
    }

    // Single language display
    return (
      <HighlightedText
        text={primaryText}
        keywords={currentQuestion.keywords}
        language={childQuizLanguage}
        showHighlights={showKeywords}
      />
    );
  };

  // Get the choices using the getChoiceContent helper
  // This uses the child's quiz language preference, not the display language
  const getChoices = () => {
    if (!currentQuestion.choices) return [];

    return currentQuestion.choices.map(choice => {
      const primaryText = getChoiceContent(choice);

      // If dual language is enabled, add the secondary text
      if (showDualLanguage && childQuizLanguage !== childMenuLanguage) {
        const secondaryText = getSecondaryChoiceContent(choice);

        // Only show dual language if the texts are different
        if (primaryText !== secondaryText) {
          return {
            id: choice.key, // Use key as id since Choice type doesn't have id
            key: choice.key,
            text: primaryText,
            secondaryText: secondaryText
          };
        }
      }

      return {
        id: choice.key, // Use key as id since Choice type doesn't have id
        key: choice.key,
        text: primaryText
      };
    });
  };

  // Handle option selection
  const handleOptionSelect = (key: string) => {
    setSelectedAnswer(key);
  };

  // Determine the question type
  const questionType = currentQuestion.type?.toString() || 'MULTIPLE_CHOICE';

  // Render the question content
  const renderQuestionContent = () => {
    const questionText = getQuestionText();

    // For multiple choice questions
    if (questionType === 'MULTIPLE_CHOICE' || questionType === 'MULTIPLE_CHOICE_IMAGE') {
      const choices = getChoices();
      return (
        <div onMouseUp={onMouseUp}>
          <div className="mb-6 text-lg font-medium text-gray-800">
            {questionText}
          </div>

          <div className="space-y-3">
            {choices.map(choice => (
              <button
                key={choice.id}
                className={`w-full text-left p-4 border-2 rounded-xl transition-all ${
                  // If this is the selected answer and it's incorrect
                  selectedAnswer === choice.key && isAnswerChecked && !isAnswerCorrect
                    ? 'border-red-500 bg-[#ffdfe0] text-red-800' // Incorrect answer
                    : // If this is the correct answer and we've checked an answer that was incorrect
                    choice.key === currentQuestion.answer && isAnswerChecked && !isAnswerCorrect
                      ? 'border-green-500 bg-[#e6f7e6] text-green-800' // Show correct answer
                      : // If this is the selected answer and it's correct or not checked yet
                      selectedAnswer === choice.key
                        ? 'border-[#04B2D9] bg-[#e0f7fa] text-[#0F5FA6]' // Selected
                        : 'border-[#d0d9e0] hover:border-[#04B2D9] hover:bg-[#eaf6ff]' // Not selected
                }`}
                onClick={() => handleOptionSelect(choice.key)}
                disabled={isAnswerChecked}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <div>{choice.text}</div>
                    {choice.secondaryText && (
                      <div className="text-gray-500 text-sm italic mt-1">{choice.secondaryText}</div>
                    )}
                  </div>
                  {/* Show a checkmark for the correct answer when an incorrect answer was selected */}
                  {choice.key === currentQuestion.answer && isAnswerChecked && !isAnswerCorrect && (
                    <span className="text-green-600 text-xl">✓</span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      );
    }

    // For true/false questions
    if (questionType === 'TRUE_FALSE') {
      return (
        <div onMouseUp={onMouseUp}>
          <div className="mb-6 text-lg font-medium text-gray-800">
            {questionText}
          </div>

          <div className="space-y-3">
            <button
              className={`w-full text-left p-4 border-2 rounded-xl transition-all ${
                // If this is the selected answer and it's incorrect
                selectedAnswer === 'true' && isAnswerChecked && !isAnswerCorrect
                  ? 'border-red-500 bg-[#ffdfe0] text-red-800' // Incorrect answer
                  : // If this is the correct answer and we've checked an answer that was incorrect
                  currentQuestion.answer === 'true' && isAnswerChecked && !isAnswerCorrect
                    ? 'border-green-500 bg-[#e6f7e6] text-green-800' // Show correct answer
                    : // If this is the selected answer and it's correct or not checked yet
                    selectedAnswer === 'true'
                      ? 'border-[#04B2D9] bg-[#e0f7fa] text-[#0F5FA6]' // Selected
                      : 'border-[#d0d9e0] hover:border-[#04B2D9] hover:bg-[#eaf6ff]' // Not selected
              }`}
              onClick={() => handleOptionSelect('true')}
              disabled={isAnswerChecked}
            >
              <div className="flex justify-between items-center">
                <span className="font-bold">True</span>
                {/* Show a checkmark for the correct answer when an incorrect answer was selected */}
                {currentQuestion.answer === 'true' && isAnswerChecked && !isAnswerCorrect && (
                  <span className="text-green-600 text-xl">✓</span>
                )}
              </div>
            </button>
            <button
              className={`w-full text-left p-4 border-2 rounded-xl transition-all ${
                // If this is the selected answer and it's incorrect
                selectedAnswer === 'false' && isAnswerChecked && !isAnswerCorrect
                  ? 'border-red-500 bg-[#ffdfe0] text-red-800' // Incorrect answer
                  : // If this is the correct answer and we've checked an answer that was incorrect
                  currentQuestion.answer === 'false' && isAnswerChecked && !isAnswerCorrect
                    ? 'border-green-500 bg-[#e6f7e6] text-green-800' // Show correct answer
                    : // If this is the selected answer and it's correct or not checked yet
                    selectedAnswer === 'false'
                      ? 'border-[#04B2D9] bg-[#e0f7fa] text-[#0F5FA6]' // Selected
                      : 'border-[#d0d9e0] hover:border-[#04B2D9] hover:bg-[#eaf6ff]' // Not selected
              }`}
              onClick={() => handleOptionSelect('false')}
              disabled={isAnswerChecked}
            >
              <div className="flex justify-between items-center">
                <span className="font-bold">False</span>
                {/* Show a checkmark for the correct answer when an incorrect answer was selected */}
                {currentQuestion.answer === 'false' && isAnswerChecked && !isAnswerCorrect && (
                  <span className="text-green-600 text-xl">✓</span>
                )}
              </div>
            </button>
          </div>
        </div>
      );
    }

    // Default fallback for other question types
    return (
      <div onMouseUp={onMouseUp}>
        <div className="mb-6 text-lg font-medium text-gray-800">
          {questionText}
        </div>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            This question type ({questionType}) is not fully supported in the V2 interface yet.
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className="p-5">
      <div className="bg-white rounded-lg p-4">
        {renderQuestionContent()}
      </div>
    </div>
  );
}
