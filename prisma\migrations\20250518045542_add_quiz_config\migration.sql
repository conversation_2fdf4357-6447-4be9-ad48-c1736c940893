-- CreateEnum
CREATE TYPE "QuizMode" AS ENUM ('MASTERY', 'TEST');

-- CreateTable
CREATE TABLE "TG_QuizConfig" (
    "id" SERIAL NOT NULL,
    "mode" "QuizMode" NOT NULL,
    "numQuestions" INTEGER NOT NULL DEFAULT 10,
    "questionTypes" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "allowTranslate" BOOLEAN NOT NULL DEFAULT true,
    "allowHints" BOOLEAN NOT NULL DEFAULT true,
    "allowAiTutor" BOOLEAN NOT NULL DEFAULT true,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TG_QuizConfig_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TG_QuizConfig_mode_key" ON "TG_QuizConfig"("mode");
