import React from 'react';

interface TeachMeModalProps {
  explanation: string;
  onClose: () => void;
  onFindEasier: () => void;
  displayLanguage: 'en' | 'zh' | 'ms';
}

const TeachMeModal: React.FC<TeachMeModalProps> = ({
  explanation,
  onClose,
  onFindEasier,
  displayLanguage
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-blue-600">
            {displayLanguage === 'en' ? 'Let\'s Learn This Concept' :
             displayLanguage === 'ms' ? 'Mari Belajar Konsep Ini' :
             '让我们学习这个概念'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <h3 className="font-semibold mb-2 text-gray-700">
            {displayLanguage === 'en' ? 'Explanation:' :
             displayLanguage === 'ms' ? 'Penjelasan:' :
             '解释:'}
          </h3>
          <div className="bg-blue-700 text-white p-4 rounded-lg">
            {explanation || (displayLanguage === 'en' ?
              'This concept requires more practice. Let\'s try an easier question to build your understanding.' :
              displayLanguage === 'ms' ?
              'Konsep ini memerlukan lebih banyak latihan. Mari cuba soalan yang lebih mudah untuk membina pemahaman anda.' :
              '这个概念需要更多的练习。让我们尝试一个更简单的问题来建立你的理解。')}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
          >
            {displayLanguage === 'en' ? 'Try Again' :
             displayLanguage === 'ms' ? 'Cuba Lagi' :
             '再试一次'}
          </button>
          <button
            onClick={onFindEasier}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            {displayLanguage === 'en' ? 'Next Question' :
             displayLanguage === 'ms' ? 'Soalan Seterusnya' :
             '下一个问题'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TeachMeModal;
