import React from 'react';
import { useQuizV2 } from './QuizV2Provider';

export default function ProgressBar() {
  const { currentIndex, questions, isFinished, phase } = useQuizV2();

  // Calculate progress percentage
  const totalQuestions = questions.length || 1;
  const progressPercentage = isFinished
    ? 100
    : ((currentIndex + 1) / totalQuestions) * 100;

  // Determine the progress bar color based on the phase
  const progressBarColor = phase === 'REVIEW' ? 'bg-yellow-500' : 'bg-blue-500';

  return (
    <div className="flex-1 px-4">
      <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
        <div
          className={`h-full ${progressBarColor} rounded-full transition-all duration-300 ease-in-out`}
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>
    </div>
  );
}
