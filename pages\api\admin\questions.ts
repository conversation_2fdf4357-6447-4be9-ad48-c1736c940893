import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const { yearId, subjectId, unitId, type, status, tpLevel } = req.query;

    console.log('Received query parameters:', { yearId, subjectId, unitId, type, status, tpLevel });

    const where: any = {};

    if (yearId) {
      where.yearId = parseInt(yearId as string);
    }
    if (subjectId) {
      where.subjectId = parseInt(subjectId as string);
    }
    if (unitId) {
      where.unitId = parseInt(unitId as string);
    }
    if (type) {
      where.type = type as string;
    }
    if (status) {
      where.status = status as string;
    }
    if (tpLevel) {
      where.tpLevel = parseInt(tpLevel as string);
    }

    const questions = await prisma.question.findMany({
      where,
      include: {
        subject: true,
        year: true,
        unit: true, // Include unit relation
      },
    });
    res.status(200).json(questions);
  } catch (error) {
    console.error('Error fetching questions:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
}
