import useSWR from 'swr';

type Mastery = { currentTp: number; confidence: 'secure' | 'emerging' | 'low' };

export function useMastery(studentId: number, unitId: number) {
  const enabled = process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2 === 'on';
  // Use a cache key that includes both studentId and unitId
  const cacheKey = enabled ? `mastery-${studentId}-${unitId}` : null;

  const { data } = useSWR<Mastery>(
    cacheKey,
    () => {
      const url = `/api/mastery/unit/${unitId}`;
      return fetch(url, {
        headers: { 'x-student-id': String(studentId) }
      }).then(r => r.json());
    },
    {
      revalidateOnFocus: false, // Reduce unnecessary revalidation
      dedupingInterval: 10000,  // Cache for 10 seconds
    }
  );

  return data;
}
