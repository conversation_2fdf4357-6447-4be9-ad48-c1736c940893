import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { Question, QuizResult } from '../../types/quiz';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { ChatMessage } from './ChatBubble';
import { v4 as uuidv4 } from 'uuid';

interface QuizContextType {
  // State
  quizAttemptId: number | null;
  questions: Question[];
  currentIndex: number;
  selectedAnswer: string;
  results: QuizResult[];
  displayLanguage: 'en' | 'zh' | 'ms';
  quizType: 'MASTERY' | 'TEST' | 'QUICK';
  isFinished: boolean;
  isAnswerIncorrect: boolean;
  isAnswerCorrect: boolean; // New state to track correct answers
  submittedAnswer: string;
  aiTutorExplanation: string | null;
  aiTutorInput: string;
  contextMenu: { x: number; y: number; text: string } | null;
  translatedText: string | null;
  startTime: number;
  isAiTutorCollapsed: boolean;
  aiTutorWidthRatio: number;
  aiTutorMessages: ChatMessage[];
  aiTutorIsLoading: boolean;
  unreadMessages: number; // Number of unread AI tutor messages
  showKeywords: boolean; // Whether to show highlighted keywords
  attempts: number; // Number of attempts for the current question
  showHint: boolean; // Whether to show the hint button
  showTeachMe: boolean; // Whether to show the "Teach Me" modal
  noKeywordsMessage: boolean; // Whether to show the "no keywords" message
  childQuizLanguage: 'EN' | 'ZH' | 'MS'; // Child's preferred quiz language
  childMenuLanguage: 'EN' | 'ZH' | 'MS'; // Child's preferred menu language

  // Actions
  setQuestions: (questions: Question[]) => void;
  setQuizAttemptId: (id: number | null) => void;
  setCurrentIndex: (index: number) => void;
  setSelectedAnswer: (answer: string) => void;
  setQuizType: (type: 'MASTERY' | 'TEST' | 'QUICK') => void;
  setIsFinished: (isFinished: boolean) => void;
  setAiTutorInput: (input: string) => void;
  setContextMenu: (menu: { x: number; y: number; text: string } | null) => void;
  handleMouseUp: (e: React.MouseEvent) => void;
  toggleLanguage: () => void;
  toggleAiTutor: () => void;
  toggleKeywords: () => void; // Toggle keyword highlighting
  setAiTutorWidthRatio: (ratio: number) => void;
  setIsAiTutorCollapsed: (collapsed: boolean) => void; // Add setIsAiTutorCollapsed to the context
  addAiTutorMessage: (sender: 'ai' | 'student', content: string) => void;
  updateLastAiMessage: (content: string) => void;
  setShowTeachMe: (show: boolean) => void; // Add setShowTeachMe to the context
  setChildQuizLanguage: (language: 'EN' | 'ZH' | 'MS') => void; // Set child's quiz language
  setChildMenuLanguage: (language: 'EN' | 'ZH' | 'MS') => void; // Set child's menu language

  // Complex actions
  handleNext: () => Promise<void>;
  handleAskAiTutor: (question: string) => Promise<void>;
  handleTranslate: () => Promise<void>;
  handleIncorrectMasteryAnswer: () => Promise<void>;
  handleShowHint: () => void;
  handleFindEasierQuestion: () => Promise<void>;

  // Helper functions
  getOriginalPrompt: (question: Question) => string;
  isChineseQuestion: (question: Question) => boolean;
}

const QuizContext = createContext<QuizContextType | undefined>(undefined);

export const useQuiz = () => {
  const context = useContext(QuizContext);
  if (context === undefined) {
    throw new Error('useQuiz must be used within a QuizProvider');
  }
  return context;
};

interface QuizProviderProps {
  children: ReactNode;
  initialQuizAttemptId?: number | null;
  initialQuestions?: Question[];
}

export const QuizProvider: React.FC<QuizProviderProps> = ({
  children,
  initialQuizAttemptId = null,
  initialQuestions = []
}) => {
  const router = useRouter();
  const { data: session, status } = useSession();

  // State
  const [quizAttemptId, setQuizAttemptId] = useState<number | null>(initialQuizAttemptId);
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [childQuizLanguage, setChildQuizLanguage] = useState<'EN' | 'ZH' | 'MS'>('ZH'); // Default to ZH
  const [childMenuLanguage, setChildMenuLanguage] = useState<'EN' | 'ZH' | 'MS'>('EN'); // Default to EN

  // Restore current index from localStorage when component mounts
  // ONLY if we don't have data from the database yet
  useEffect(() => {
    if (quizAttemptId && currentIndex === 0) {
      try {
        const savedIndex = localStorage.getItem(`quiz_index_${quizAttemptId}`);
        if (savedIndex !== null) {
          const parsedIndex = parseInt(savedIndex, 10);
          if (!isNaN(parsedIndex) && parsedIndex >= 0) {
            console.log('Temporarily restoring saved index from localStorage:', parsedIndex);
            setCurrentIndex(parsedIndex);

            // This is just a temporary value until we get the real value from the database
            console.log('This value will be overridden by the database value when loaded');
          }
        }
      } catch (error) {
        console.error('Error restoring index from localStorage:', error);
      }
    }
  }, [quizAttemptId]);

  // Restore child language preferences from localStorage when component mounts
  useEffect(() => {
    try {
      const savedQuizLanguage = localStorage.getItem('childQuizLanguage');
      const savedMenuLanguage = localStorage.getItem('childMenuLanguage');

      if (savedQuizLanguage) {
        console.log('Restoring quiz language from localStorage:', savedQuizLanguage);
        setChildQuizLanguage(savedQuizLanguage as 'EN' | 'ZH' | 'MS');
      }

      if (savedMenuLanguage) {
        console.log('Restoring menu language from localStorage:', savedMenuLanguage);
        setChildMenuLanguage(savedMenuLanguage as 'EN' | 'ZH' | 'MS');
      }
    } catch (error) {
      console.error('Error restoring language preferences from localStorage:', error);
    }
  }, []);
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [results, setResults] = useState<Array<{ questionId: number; chosenAnswer: string; isCorrect: boolean | null; timeSpentSeconds: number | null }>>([]);
  const [isFinished, setIsFinished] = useState(false);
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; text: string } | null>(null);
  const [translatedText, setTranslatedText] = useState<string | null>(null);
  const [displayLanguage, setDisplayLanguage] = useState<'en' | 'zh' | 'ms'>('en');
  const [aiTutorExplanation, setAiTutorExplanation] = useState<string | null>(null);
  const [aiTutorInput, setAiTutorInput] = useState<string>('');
  const [startTime, setStartTime] = useState<number>(Date.now());
  const [quizType, setQuizType] = useState<'MASTERY' | 'TEST' | 'QUICK'>('TEST');
  const [isAnswerIncorrect, setIsAnswerIncorrect] = useState<boolean>(false);
  const [isAnswerCorrect, setIsAnswerCorrect] = useState<boolean>(false);
  const [submittedAnswer, setSubmittedAnswer] = useState<string>('');
  const [isAiTutorCollapsed, setIsAiTutorCollapsed] = useState<boolean>(true); // Start with AI tutor collapsed
  const [aiTutorWidthRatio, setAiTutorWidthRatio] = useState<number>(0.6); // Default AI tutor width ratio (60%)
  const [aiTutorMessages, setAiTutorMessages] = useState<ChatMessage[]>([]);
  const [aiTutorIsLoading, setAiTutorIsLoading] = useState<boolean>(false);
  const [unreadMessages, setUnreadMessages] = useState<number>(0); // Track unread messages
  const [showKeywords, setShowKeywords] = useState<boolean>(false);
  const [attempts, setAttempts] = useState<number>(0);
  const [showHint, setShowHint] = useState<boolean>(false);
  const [showTeachMe, setShowTeachMe] = useState<boolean>(false);
  const [noKeywordsMessage, setNoKeywordsMessage] = useState<boolean>(false);

  // Add a message to the AI tutor chat
  const addAiTutorMessage = useCallback((sender: 'ai' | 'student', content: string) => {
    console.log('🔄 addAiTutorMessage called with:', { sender, content });

    const newMessage: ChatMessage = {
      id: uuidv4(),
      sender,
      content,
      timestamp: new Date()
    };

    setAiTutorMessages(prev => {
      const newMessages = [...prev, newMessage];
      console.log('🔄 aiTutorMessages updated:', {
        prevCount: prev.length,
        newCount: newMessages.length,
        latestMessage: newMessage,
        allMessages: newMessages
      });

      // Store messages in localStorage for debugging
      try {
        localStorage.setItem('debug_ai_tutor_messages', JSON.stringify(newMessages));
        console.log('💾 Saved messages to localStorage');
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }

      return newMessages;
    });

    // If the message is from the AI and the panel is collapsed, increment the unread count
    if (sender === 'ai' && isAiTutorCollapsed) {
      setUnreadMessages(prev => prev + 1);
      console.log('📬 Incremented unread messages count');
    }

    // Log the current state after a short delay to see if it persists
    setTimeout(() => {
      console.log('⏱️ Delayed check of aiTutorMessages:', aiTutorMessages);
    }, 500);
  }, [isAiTutorCollapsed, aiTutorMessages]);

  // Expose the addAiTutorMessage function globally for direct access
  useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('🌐 Exposing addAiTutorMessage function globally');
      (window as any).__addAiTutorMessage = (sender: 'ai' | 'student', content: string) => {
        console.log('🌐 Global __addAiTutorMessage called with:', { sender, content });
        addAiTutorMessage(sender, content);
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        console.log('🌐 Removing global __addAiTutorMessage function');
        delete (window as any).__addAiTutorMessage;
      }
    };
  }, [addAiTutorMessage]);

  // Update the last AI message (used for updating "thinking..." messages)
  const updateLastAiMessage = useCallback((content: string) => {
    setAiTutorMessages(prev => {
      const lastAiMessageIndex = [...prev].reverse().findIndex(m => m.sender === 'ai');
      if (lastAiMessageIndex === -1) return prev;

      const actualIndex = prev.length - 1 - lastAiMessageIndex;
      const newMessages = [...prev];
      newMessages[actualIndex] = {
        ...newMessages[actualIndex],
        content
      };
      return newMessages;
    });
  }, []);

  const total = questions.length;
  const currentQuestion = questions[currentIndex];

  // Helper function to get the prompt in the original language
  const getOriginalPrompt = (question: Question) => {
    if (!question) return '';

    // Check the originalLanguage field and return the appropriate prompt
    if (question.originalLanguage === 'ZH') {
      return question.promptZh || '';
    } else if (question.originalLanguage === 'EN') {
      return question.promptEn || '';
    } else if (question.originalLanguage === 'MS') {
      return question.promptMs || question.promptEn || '';
    }

    // Default to English if originalLanguage is not specified
    return question.promptEn || question.promptZh || '';
  };

  // Helper function to determine if the question is in Chinese
  const isChineseQuestion = (question: Question) => {
    return question && question.originalLanguage === 'ZH';
  };

  // Reset start time and answer state when current question changes
  useEffect(() => {
    setStartTime(Date.now());
    setIsAnswerIncorrect(false);
    setIsAnswerCorrect(false);
    setSubmittedAnswer('');
    setAttempts(0);
    setShowHint(false);
    setShowTeachMe(false);
    setShowKeywords(false);
    setNoKeywordsMessage(false);

    // Log when current index changes
    console.log('QuizContext: Current index changed to:', currentIndex);

    // Save the current index to localStorage as a backup
    if (quizAttemptId) {
      try {
        localStorage.setItem(`quiz_index_${quizAttemptId}`, currentIndex.toString());
      } catch (error) {
        console.error('Error saving current index to localStorage:', error);
      }
    }
  }, [currentIndex, quizAttemptId]);

  // Add a welcome message when the component mounts
  useEffect(() => {
    // Only add the welcome message if there are no messages yet
    if (aiTutorMessages.length === 0) {
      const welcomeMessage = displayLanguage === 'en'
        ? 'Hello! I\'m your AI tutor. Ask me anything about this question!'
        : '你好！我是你的AI导师。问我任何关于这个问题的事情！';

      addAiTutorMessage('ai', welcomeMessage);
    }
  }, [aiTutorMessages.length, displayLanguage, addAiTutorMessage]);

  // Monitor changes to aiTutorMessages
  useEffect(() => {
    console.log('🔍 aiTutorMessages changed:', {
      count: aiTutorMessages.length,
      messages: aiTutorMessages
    });

    // Check if there are any translation messages
    const translationMessages = aiTutorMessages.filter(msg =>
      msg.content.includes('Translation of "') ||
      msg.content.includes('翻译 "') ||
      msg.content.includes('Terjemahan untuk "')
    );

    console.log('🔍 Translation messages:', {
      count: translationMessages.length,
      messages: translationMessages
    });

    // Store in localStorage for debugging
    try {
      localStorage.setItem('debug_ai_tutor_messages_state', JSON.stringify(aiTutorMessages));
      localStorage.setItem('debug_translation_messages', JSON.stringify(translationMessages));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }, [aiTutorMessages]);

  // Listen for translation complete events and add them to the AI tutor chat
  useEffect(() => {
    console.log('🔍 Setting up translation event listeners');

    const handleTranslationComplete = (event: CustomEvent) => {
      console.log('🎯 translationComplete event received:', event);

      if (event.detail && event.detail.translatedText && event.detail.originalText) {
        // Create translation message based on the current display language
        const translationPrefix = displayLanguage === 'en'
          ? 'Translation of "'
          : displayLanguage === 'ms'
            ? 'Terjemahan untuk "'
            : '翻译 "';

        const translationSuffix = displayLanguage === 'en'
          ? '": '
          : displayLanguage === 'ms'
            ? '": '
            : '": ';

        const message = `${translationPrefix}${event.detail.originalText}${translationSuffix}${event.detail.translatedText}`;

        console.log('📝 Preparing to add translation to AI tutor chat:', {
          displayLanguage,
          message,
          aiTutorMessagesCount: aiTutorMessages.length
        });

        // Add the message to the AI tutor chat
        addAiTutorMessage('ai', message);

        console.log('✅ Translation added to AI tutor chat:', {
          originalText: event.detail.originalText,
          translatedText: event.detail.translatedText,
          message,
          newAiTutorMessagesCount: aiTutorMessages.length + 1
        });

        // Don't automatically expand the AI tutor panel
        // Instead, we'll increment the unread message count or show a notification
        // The translation will be visible when the user opens the AI tutor
      } else {
        console.warn('⚠️ translationComplete event received but missing required data:', event);
      }
    };

    // Handle the direct translation message event
    const handleDirectTranslationMessage = (event: CustomEvent) => {
      console.log('🎯 directTranslationMessage event received:', event);

      if (event.detail && event.detail.message && event.detail.sender) {
        console.log('📝 Preparing to add direct translation to AI tutor chat:', {
          message: event.detail.message,
          sender: event.detail.sender,
          aiTutorMessagesCount: aiTutorMessages.length
        });

        // Add the message directly to the AI tutor chat
        addAiTutorMessage(event.detail.sender, event.detail.message);

        console.log('✅ Direct translation added to AI tutor chat:', {
          message: event.detail.message,
          newAiTutorMessagesCount: aiTutorMessages.length + 1
        });
      } else {
        console.warn('⚠️ directTranslationMessage event received but missing required data:', event);
      }
    };

    // Listen for the custom events
    window.addEventListener('translationComplete', handleTranslationComplete as EventListener);
    window.addEventListener('directTranslationMessage', handleDirectTranslationMessage as EventListener);

    // Test the event listeners with manual dispatches
    setTimeout(() => {
      console.log('🧪 Testing translation event listeners with manual dispatches');

      // Test translationComplete event
      const testEvent1 = new CustomEvent('translationComplete', {
        detail: {
          translatedText: '[TEST] This is a test translation',
          originalText: '[TEST] 这是一个测试翻译'
        }
      });
      window.dispatchEvent(testEvent1);

      // Test directTranslationMessage event
      const testEvent2 = new CustomEvent('directTranslationMessage', {
        detail: {
          message: '[TEST] Translation of "这是一个直接测试翻译": This is a direct test translation',
          sender: 'ai',
          translatedText: '[TEST] This is a direct test translation',
          originalText: '[TEST] 这是一个直接测试翻译'
        }
      });
      window.dispatchEvent(testEvent2);
    }, 3000);

    return () => {
      console.log('🧹 Cleaning up translation event listeners');
      window.removeEventListener('translationComplete', handleTranslationComplete as EventListener);
      window.removeEventListener('directTranslationMessage', handleDirectTranslationMessage as EventListener);
    };
  }, [displayLanguage, addAiTutorMessage, aiTutorMessages.length]);

  // Toggle language
  const toggleLanguage = () => {
    setDisplayLanguage(prev => {
      if (prev === 'en') return 'zh';
      if (prev === 'zh') return 'ms';
      return 'en';
    });
  };

  // Toggle AI tutor visibility
  const toggleAiTutor = () => {
    const newCollapsedState = !isAiTutorCollapsed;
    setIsAiTutorCollapsed(newCollapsedState);

    // If expanding the panel, reset the unread count
    if (!newCollapsedState) {
      setUnreadMessages(0);
      console.log('📭 Reset unread messages count');
    }
  };

  // Toggle keyword highlighting
  const toggleKeywords = () => {
    // Check if the current question has keywords to highlight
    if (currentQuestion) {
      const hasKeywords = !!currentQuestion.keywords;

      // If keywords exist, toggle keyword highlighting
      if (hasKeywords) {
        // Check if there are keywords for the current language
        const language = currentQuestion.originalLanguage || 'EN';
        const keywordsExist =
          (language === 'EN' && currentQuestion.keywords?.en?.length > 0) ||
          (language === 'ZH' && currentQuestion.keywords?.zh?.length > 0) ||
          (language === 'MS' && currentQuestion.keywords?.ms?.length > 0);

        if (keywordsExist) {
          setShowKeywords(prev => !prev);
          setNoKeywordsMessage(false);
        } else {
          // No keywords for the current language
          setShowKeywords(false);
          setNoKeywordsMessage(true);
        }
      } else {
        // No keywords at all
        setShowKeywords(false);
        setNoKeywordsMessage(true);
      }
    } else {
      setShowKeywords(false);
      setNoKeywordsMessage(true);
    }
  };

  // Handle mouse up for text selection
  const handleMouseUp = (e: React.MouseEvent) => {
    // Check if translation is allowed based on quiz attempt metadata
    const allowTranslate = quizAttemptId ?
      localStorage.getItem(`quiz_${quizAttemptId}_allowTranslate`) !== 'false' : true;

    // Only show translation menu if allowed
    if (allowTranslate) {
      const selectedText = window.getSelection()?.toString().trim();
      if (selectedText) {
        setContextMenu({ x: e.pageX, y: e.pageY, text: selectedText });
        setTranslatedText(null); // Clear previous translation
      }
    }
  };

  // Handle Next / Submit for both types
  const handleNext = async () => {
    if (!currentQuestion || quizAttemptId === null) return;

    // If we're in mastery mode and showing the Teach Me modal, just hide it and proceed
    if (quizType === 'MASTERY' && showTeachMe) {
      setShowTeachMe(false);

      // If this is the last question, finish the quiz
      if (currentIndex >= questions.length - 1) {
        setIsFinished(true);
      } else {
        // Otherwise, move to the next question
        setCurrentIndex(currentIndex + 1);
        setSelectedAnswer('');
      }
      return;
    }

    // If we already have a correct answer and the user is clicking "CONTINUE"
    if (isAnswerCorrect && submittedAnswer === selectedAnswer) {
      // Clear all state related to the current question
      setAiTutorExplanation(null); // Clear explanation
      setIsAnswerIncorrect(false); // Reset incorrect answer state
      setIsAnswerCorrect(false); // Reset correct answer state
      setSubmittedAnswer(''); // Reset submitted answer
      setAttempts(0); // Reset attempts
      setShowHint(false); // Hide hint
      setShowTeachMe(false); // Hide teach me modal
      setShowKeywords(false); // Reset keyword highlighting
      setNoKeywordsMessage(false); // Reset no keywords message

      // Check if this is the last question
      const isLastQuestion = currentIndex === total - 1;

      // Move to the next question or finish
      if (isLastQuestion) {
        setIsFinished(true);
      } else {
        setCurrentIndex(currentIndex + 1);
        setSelectedAnswer('');
      }

      return; // Exit early - no need to submit the answer again
    }

    // If the user has already submitted an incorrect answer and is trying again with a new answer
    if (isAnswerIncorrect && submittedAnswer && selectedAnswer !== submittedAnswer) {
      // Reset the incorrect state since they're trying a new answer
      setIsAnswerIncorrect(false);
    }

    const timeSpent = Date.now() - startTime;

    // Increment attempts counter
    const newAttempts = attempts + 1;
    setAttempts(newAttempts);

    // Store the submitted answer
    setSubmittedAnswer(selectedAnswer);

    // Prepare the submission data based on question type
    const questionType = currentQuestion.type ? currentQuestion.type.toString() : '';
    let submissionData: any = {
      quizAttemptId: quizAttemptId,
      questionId: currentQuestion.id,
      childId: session?.user?.id ? Number(session.user.id) : 1,
      quizType: quizType.toLowerCase(), // Convert to lowercase to match the expected format in the API
      attemptNumber: newAttempts, // Add the attempt number
    };

    // Determine which field to use based on question type
    if (questionType === 'MULTIPLE_CHOICE' || questionType === 'MULTIPLE_CHOICE_IMAGE' || questionType === 'TRUE_FALSE') {
      submissionData.submittedKey = selectedAnswer;
    } else if (questionType === 'MULTI_CHOICE') {
      try {
        const selectedKeys = JSON.parse(selectedAnswer);
        submissionData.submittedJson = { keys: selectedKeys };
      } catch (error) {
        console.error('Error parsing multi-choice answer:', error);
        submissionData.submittedJson = { keys: [] };
      }
    } else if (questionType === 'SHORT_ANSWER' || questionType === 'FILL_IN_THE_BLANK') {
      submissionData.submittedText = selectedAnswer;
    } else if (questionType === 'MATCHING') {
      try {
        const matches = JSON.parse(selectedAnswer);
        submissionData.submittedJson = { pairs: matches };
      } catch (error) {
        console.error('Error parsing matching answer:', error);
        submissionData.submittedJson = { pairs: {} };
      }
    } else if (questionType === 'SEQUENCING') {
      try {
        const sequence = JSON.parse(selectedAnswer);
        submissionData.submittedJson = { order: sequence };
      } catch (error) {
        console.error('Error parsing sequencing answer:', error);
        submissionData.submittedJson = { order: [] };
      }
    } else if (questionType === 'LONG_ANSWER') {
      submissionData.submittedText = selectedAnswer;
    } else {
      submissionData.submittedText = selectedAnswer;
    }

    // Submit the answer to the API endpoint
    try {
      const response = await fetch('/api/submit-answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`Error submitting answer: ${response.statusText}`);
      }

      const result = await response.json();
      const isCorrect = result.correct;
      const isLastQuestion = result.isLastQuestion;

      // Update answer state based on correctness
      if (!isCorrect && quizType === 'MASTERY') {
        setIsAnswerIncorrect(true);
        setIsAnswerCorrect(false);

        // Show hint after first wrong attempt
        if (newAttempts === 1) {
          setShowHint(true);
        }

        // Show "Teach Me" modal after three wrong attempts
        if (newAttempts >= 3) {
          setShowTeachMe(true);
        }
      } else {
        // For correct answers, always update both states
        setIsAnswerIncorrect(false);
        setIsAnswerCorrect(true);

        // Log that we've set the correct answer state
        console.log('Setting isAnswerCorrect to true for question:', currentQuestion.id);
      }

      // Update results state locally for display
      const updatedResults = [...results];
      updatedResults[currentIndex] = {
        questionId: currentQuestion.id,
        chosenAnswer: selectedAnswer,
        isCorrect: isCorrect,
        timeSpentSeconds: Math.round(timeSpent / 1000),
      };
      setResults(updatedResults);

      // For mastery quizzes, if the answer is incorrect, show AI tutor explanation and don't advance
      if (quizType === 'MASTERY' && !isCorrect) {
        // If we've reached 3 attempts and showing the "Teach Me" modal, don't fetch AI tutor explanation
        if (newAttempts >= 3) {
          return; // Prevent progression and wait for user to interact with the "Teach Me" modal
        }

        await handleIncorrectMasteryAnswer();
        return; // Prevent progression on wrong answer in mastery mode
      }

      // Check if this is the last question
      const isActuallyLastQuestion = isLastQuestion || currentIndex === total - 1;

      // If answer is correct or in Test Mode, proceed to next question or finish
      handleCorrectOrTestAnswer(isActuallyLastQuestion);

    } catch (error) {
      console.error('Error submitting answer:', error);
    }
  };

  // Handle incorrect answers in mastery mode
  const handleIncorrectMasteryAnswer = async () => {
    if (!currentQuestion) return;

    // Check if AI tutor is allowed based on quiz attempt metadata
    const allowAiTutor = quizAttemptId ?
      localStorage.getItem(`quiz_${quizAttemptId}_allowAiTutor`) !== 'false' : true;

    if (!allowAiTutor) {
      console.log('AI tutor is disabled for this quiz');
      return;
    }

    // Clear any previous explanation first
    setAiTutorExplanation(null);
    // For both multiple choice and short answer questions
    setAiTutorExplanation('Fetching explanation...');

    // Add a "thinking" message from the AI
    const thinkingMessage = displayLanguage === 'en' ? 'Analyzing your answer...' : '分析您的答案...';
    addAiTutorMessage('ai', thinkingMessage);

    setAiTutorIsLoading(true);

    try {
      // TODO: Get actual year and subject
      const year = 'Year 5'; // Placeholder
      const subject = 'Science'; // Placeholder

      // Find the full text of the incorrect answer
      let incorrectAnswerText = selectedAnswer || '';
      let correctAnswerText = currentQuestion?.answer || '';

      // For multiple choice questions, get the full text of the answers
      if (currentQuestion) {
        const questionType = currentQuestion.type.toString();
        if (questionType === 'MULTIPLE_CHOICE' || questionType === 'MULTIPLE_CHOICE_IMAGE' || questionType === 'TRUE_FALSE') {
          // Determine which language to use based on the question's original language
          const useEnglish = currentQuestion.originalLanguage === 'EN';
          const useMalay = currentQuestion.originalLanguage === 'MS';
          const useChinese = currentQuestion.originalLanguage === 'ZH' || (!useEnglish && !useMalay);

          // Find the choice that matches the incorrect answer key
          const incorrectChoice = currentQuestion.choices?.find(choice => choice.key === selectedAnswer);
          if (incorrectChoice) {
            if (useEnglish) {
              incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textEn}`;
            } else if (useChinese) {
              incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textZh}`;
            } else if (useMalay) {
              // If Malay is available, use it, otherwise fall back to English
              incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textMs || incorrectChoice.textEn}`;
            } else {
              incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textEn}`;
            }
          }

          // Find the choice that matches the correct answer key
          const correctChoice = currentQuestion.choices?.find(choice => choice.key === currentQuestion.answer);
          if (correctChoice) {
            if (useEnglish) {
              correctAnswerText = `${correctChoice.key}. ${correctChoice.textEn}`;
            } else if (useChinese) {
              correctAnswerText = `${correctChoice.key}. ${correctChoice.textZh}`;
            } else if (useMalay) {
              // If Malay is available, use it, otherwise fall back to English
              correctAnswerText = `${correctChoice.key}. ${correctChoice.textMs || correctChoice.textEn}`;
            } else {
              correctAnswerText = `${correctChoice.key}. ${correctChoice.textEn}`;
            }
          }
        }
      }

      const response = await fetch('/api/ai-tutor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          year,
          subject,
          question: currentQuestion,
          correctAnswer: correctAnswerText,
          incorrectAnswer: incorrectAnswerText,
          language: displayLanguage,
          topicEn: currentQuestion?.topic,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error fetching AI tutor explanation: ${response.statusText}`);
      }

      const data = await response.json();
      const explanation = data.explanation + ' ' + data.hints;
      setAiTutorExplanation(explanation);

      // Update the last AI message with the actual response
      updateLastAiMessage(explanation);
    } catch (error: any) {
      console.error('AI Tutor API error:', error);
      const errorMessage = `Error fetching explanation: ${error.message}`;
      setAiTutorExplanation(errorMessage);

      // Update the last AI message with the error
      updateLastAiMessage(displayLanguage === 'en'
        ? `Sorry, I encountered an error: ${error.message}`
        : `抱歉，我遇到了一个错误: ${error.message}`);
    } finally {
      setAiTutorIsLoading(false);
    }
  };

  // Handle correct answers or test mode
  const handleCorrectOrTestAnswer = (isLastQuestion: boolean) => {
    // For correct answers, show the green continue button and wait for user to click it
    if (isAnswerCorrect) {
      // Don't automatically advance - just show the correct answer feedback
      // The user will need to click the CONTINUE button to proceed

      // Only auto-advance in non-mastery modes
      if (quizType !== 'MASTERY') {
        // Wait 1.5 seconds before moving to the next question
        setTimeout(() => {
          // Clear all state related to the current question
          setAiTutorExplanation(null); // Clear explanation
          setIsAnswerIncorrect(false); // Reset incorrect answer state
          setIsAnswerCorrect(false); // Reset correct answer state
          setSubmittedAnswer(''); // Reset submitted answer
          setAttempts(0); // Reset attempts
          setShowHint(false); // Hide hint
          setShowTeachMe(false); // Hide teach me modal
          setShowKeywords(false); // Reset keyword highlighting
          setNoKeywordsMessage(false); // Reset no keywords message

          // Only consider the quiz finished if we've actually completed all questions
          // or if the server explicitly says it's the last question
          if (isLastQuestion || (currentIndex + 1 >= total)) {
            setIsFinished(true);
          } else {
            setCurrentIndex(currentIndex + 1);
            setSelectedAnswer('');
          }
        }, 1500); // 1.5 second delay
      }
    } else if (quizType !== 'MASTERY') {
      // For incorrect answers in test mode, proceed immediately
      // But don't auto-advance in mastery mode - let the user see the feedback

      // Clear all state related to the current question
      setAiTutorExplanation(null); // Clear explanation
      setIsAnswerIncorrect(false); // Reset incorrect answer state
      setIsAnswerCorrect(false); // Reset correct answer state
      setSubmittedAnswer(''); // Reset submitted answer
      setAttempts(0); // Reset attempts
      setShowHint(false); // Hide hint
      setShowTeachMe(false); // Hide teach me modal
      setShowKeywords(false); // Reset keyword highlighting
      setNoKeywordsMessage(false); // Reset no keywords message

      if (!isLastQuestion && currentIndex + 1 < total) {
        setCurrentIndex(currentIndex + 1);
        setSelectedAnswer('');
      } else {
        setIsFinished(true);
      }
    }
  };

  // Show hint for the current question
  const handleShowHint = () => {
    // Check if hints are allowed based on quiz attempt metadata
    const allowHints = quizAttemptId ?
      localStorage.getItem(`quiz_${quizAttemptId}_allowHints`) !== 'false' : true;

    if (!allowHints) {
      console.log('Hints are disabled for this quiz');
      return;
    }

    setShowHint(true);

    // Check if the current question has keywords to highlight
    if (currentQuestion) {
      const hasKeywords = !!currentQuestion.keywords;

      // If keywords exist, enable keyword highlighting
      if (hasKeywords) {
        // Check if there are keywords for the current language
        const language = currentQuestion.originalLanguage || 'EN';
        const keywordsExist =
          (language === 'EN' && currentQuestion.keywords?.en?.length > 0) ||
          (language === 'ZH' && currentQuestion.keywords?.zh?.length > 0) ||
          (language === 'MS' && currentQuestion.keywords?.ms?.length > 0);

        if (keywordsExist) {
          setShowKeywords(true);
          setNoKeywordsMessage(false);
        } else {
          // No keywords for the current language
          setShowKeywords(false);
          setNoKeywordsMessage(true);
        }
      } else {
        // No keywords at all
        setShowKeywords(false);
        setNoKeywordsMessage(true);
      }
    } else {
      setShowKeywords(false);
      setNoKeywordsMessage(true);
    }
  };

  // Find an easier question (lower TP level)
  const handleFindEasierQuestion = async () => {
    if (!currentQuestion) return;

    // Get the current TP level (default to 3 if not available)
    const currentTpLevel = (currentQuestion as any).tpLevel || 3;

    // Only proceed if we can go lower
    if (currentTpLevel <= 1) {
      // If already at TP1, just close the modal and try again
      setShowTeachMe(false);
      return;
    }

    // Find a question with a lower TP level
    // Note: targetTpLevel is defined but not used yet - will be used in future implementation
    // const targetTpLevel = currentTpLevel - 1;

    // TODO: Implement API call to fetch an easier question
    // First check if this is the last question
    const isLastQuestion = currentIndex >= total - 1;

    // Close the modal
    setShowTeachMe(false);

    // If this is the last question, finish the quiz
    if (isLastQuestion) {
      console.log('Last question reached, finishing quiz');
      setIsFinished(true);
    } else {
      // Otherwise, move to the next question
      setCurrentIndex(currentIndex + 1);
      setSelectedAnswer('');
    }
  };

  // Function for asking AI Tutor
  const handleAskAiTutor = async (question: string) => {
    if (!question.trim() || !currentQuestion) return;

    // Store the current input and clear the input field
    setAiTutorInput('');

    // Add the student's message to the chat
    addAiTutorMessage('student', question);

    // Add a "thinking" message from the AI
    const thinkingMessage = displayLanguage === 'en' ? 'Thinking...' : '思考中...';
    addAiTutorMessage('ai', thinkingMessage);

    setAiTutorIsLoading(true);

    try {
      // Prepare the question object with the necessary properties
      const questionData = currentQuestion ? {
        id: currentQuestion.id,
        promptEn: currentQuestion.promptEn || '',
        promptZh: currentQuestion.promptZh || '',
        topic: currentQuestion.topic || '',
        type: currentQuestion.type || ''
      } : null;

      const response = await fetch('/api/ai-tutor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: questionData,
          userQuestion: question,
          language: displayLanguage,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error getting AI tutor response: ${response.statusText}`);
      }

      const data = await response.json();

      // Update the last AI message with the actual response
      updateLastAiMessage(data.explanation);
    } catch (error: any) {
      console.error('AI Tutor API error:', error);

      // Update the last AI message with the error
      updateLastAiMessage(displayLanguage === 'en'
        ? `Sorry, I encountered an error: ${error.message}`
        : `抱歉，我遇到了一个错误: ${error.message}`);
    } finally {
      setAiTutorIsLoading(false);
    }
  };

  // Handle translation
  const handleTranslate = async () => {
    if (!contextMenu || !currentQuestion) return;

    setTranslatedText('Translating...'); // Show loading indicator
    try {
      const response = await fetch('/api/ai-translator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          highlightedText: contextMenu.text,
          question: currentQuestion.promptZh || ''
        }),
      });

      const data = await response.json();
      if (data.translatedText) {
        setTranslatedText(data.translatedText); // Display translated text

        // Log debug information if available
        if (data.debug) {
          console.log('Translation debug info:', data.debug);
        }

        // Dispatch a custom event with the translation result
        const translationCompleteEvent = new CustomEvent('translationComplete', {
          detail: {
            translatedText: data.translatedText,
            originalText: contextMenu.text
          }
        });
        window.dispatchEvent(translationCompleteEvent);

        // Log the translation
        try {
          await fetch('/api/log-translation', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              childId: session?.user?.id ? Number(session.user.id) : 1, // Get childId from auth context with fallback
              questionId: currentQuestion.id,
              translatedText: contextMenu.text,
            }),
          });
        } catch (logError) {
          console.error('Error logging translation:', logError);
        }

      } else {
        setTranslatedText('Translation failed.'); // Show error message
      }
    } catch (error) {
      console.error('Translation error:', error);
      setTranslatedText('Translation failed.'); // Show error message
    }
  };

  const value = {
    // State
    quizAttemptId,
    questions,
    currentIndex,
    selectedAnswer,
    results,
    displayLanguage,
    quizType,
    isFinished,
    isAnswerIncorrect,
    isAnswerCorrect,
    submittedAnswer,
    aiTutorExplanation,
    aiTutorInput,
    contextMenu,
    translatedText,
    startTime,
    isAiTutorCollapsed,
    aiTutorWidthRatio,
    aiTutorMessages,
    aiTutorIsLoading,
    unreadMessages,
    showKeywords,
    attempts,
    showHint,
    showTeachMe,
    noKeywordsMessage,
    childQuizLanguage,
    childMenuLanguage,

    // Actions
    setQuestions,
    setQuizAttemptId,
    setCurrentIndex,
    setSelectedAnswer,
    setQuizType,
    setIsFinished,
    setAiTutorInput,
    setContextMenu,
    handleMouseUp,
    toggleLanguage,
    toggleAiTutor,
    toggleKeywords,
    setAiTutorWidthRatio,
    setIsAiTutorCollapsed,
    addAiTutorMessage,
    updateLastAiMessage,
    setShowTeachMe,
    setChildQuizLanguage,
    setChildMenuLanguage,

    // Complex actions
    handleNext,
    handleAskAiTutor,
    handleTranslate,
    handleIncorrectMasteryAnswer,
    handleShowHint,
    handleFindEasierQuestion,

    // Helper functions
    getOriginalPrompt,
    isChineseQuestion,
  };

  return (
    <QuizContext.Provider value={value}>
      {children}
    </QuizContext.Provider>
  );
};

export default QuizContext;

