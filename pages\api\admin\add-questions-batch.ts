import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const questionsData = req.body;

  if (!Array.isArray(questionsData)) {
    return res.status(400).json({ message: 'Invalid request body. Expected an array of questions.' });
  }

  const results: any[] = [];
  const errors: any[] = [];

  for (const questionData of questionsData) {
    // Basic validation for each question
    if (!questionData || !questionData.type || !questionData.year || !questionData.subject || !questionData.unit || !questionData.prompt) {
      errors.push({ question: questionData, message: 'Missing required question data' });
      continue; // Skip to the next question
    }

    try {
      // Generate a unique questionId (simple timestamp-based for now)
      const questionId = `q${Date.now()}-${Math.random().toString(36).substring(2, 8)}`; // Added random string for better uniqueness

      // Find or create related Subject, Year, and Unit
      const yearRecord = await prisma.year.upsert({
        where: { yearNumber: questionData.year },
        update: {},
        create: { yearNumber: questionData.year },
      });

      const subjectRecord = await prisma.subject.upsert({
        where: { name: questionData.subject },
        update: {},
        create: { name: questionData.subject },
      });

      const unitRecord = await prisma.unit.upsert({
        where: {
          unitNumber_subjectId_yearId: {
            unitNumber: questionData.unit,
            subjectId: subjectRecord.id,
            yearId: yearRecord.id,
          },
        },
        update: {},
        create: {
          unitNumber: questionData.unit,
          topicEn: questionData.topic || '', // Use topic from JSON or empty string
          topicZh: '', // Assuming Chinese topic is not required initially
          subject: { connect: { id: subjectRecord.id } },
          year: { connect: { id: yearRecord.id } },
        },
      });

      // Create the Question record
      const newQuestion = await prisma.question.create({
        data: {
          questionId: questionId,
          type: questionData.type.toUpperCase(), // Ensure type is uppercase for enum
          promptEn: questionData.prompt.en || '',
          promptZh: questionData.prompt.zh || '',
          promptMs: questionData.prompt.ms || '',
          originalLanguage: questionData.originalLanguage || 'ZH', // Default to Chinese if not specified
          translationState: questionData.translationState || 'PARTIAL', // Default to PARTIAL if not specified
          subTopicEn: '', // Add empty string for subTopicEn
          subTopicZh: questionData.topic || '', // Map topic from JSON to subTopicZn
          subTopicMs: '', // Add empty string for subTopicMs
          subject: { connect: { id: subjectRecord.id } },
          year: { connect: { id: yearRecord.id } },
          unit: { connect: { id: unitRecord.id } },
          explanation: questionData.explanation ? {
            create: {
              textEn: questionData.explanation.en || '',
              textZh: questionData.explanation.zh || '',
              textMs: questionData.explanation.ms || '',
            },
          } : undefined,
          choices: questionData.choices ? {
            create: questionData.choices.map((choice: any) => ({
              key: choice.key,
              textEn: choice.en || '',
              textZh: choice.zh || '',
          })),
        } : undefined,
        answer: (questionData.type.toUpperCase() === 'MULTIPLE_CHOICE' && (questionData.answer?.key || (questionData.answers && questionData.answers.length > 0))) ? {
           create: {
             key: questionData.answer?.key || questionData.answers[0], // Use key from answer object or first element of answers array
             textEn: questionData.answer?.en || '', // Include text if available (for consistency, though not strictly needed for MC answer key)
             textZh: questionData.answer?.zh || '', // Include text if available
           }
        } : (questionData.type.toUpperCase() === 'SHORT_ANSWER' && questionData.answer?.en) ? {
           create: {
             key: null, // Short answer doesn't use a key
             textEn: questionData.answer.en,
             textZh: questionData.answer.zh || '',
           }
        } : undefined, // Don't create answer if no valid data for either type
      },
      include: {
        choices: true,
        answer: true,
        explanation: true,
      },
    });
      results.push(newQuestion);
    } catch (error) {
      console.error('Error adding question:', error);
      errors.push({ question: questionData, message: error.message });
    }
  }

  if (errors.length > 0) {
    res.status(400).json({ message: 'Some questions failed to add', results, errors });
  } else {
    res.status(201).json({ message: 'All questions added successfully', results });
  }
}
