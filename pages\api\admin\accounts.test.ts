import request from 'supertest';
import handler from './accounts';
import { createMocks } from 'node-mocks-http';

describe('/api/admin/accounts API', () => {
  it('should return a list of accounts with status 200', async () => {
    const { req, res } = createMocks({ method: 'GET' });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(Array.isArray(data)).toBe(true);
  });

  it('should return 405 for unsupported methods', async () => {
    const { req, res } = createMocks({ method: 'POST' });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
  });
});