import { Answer, StudentAnswer } from '@prisma/client';

/**
 * Grades a student answer against the correct answer
 * @param answer The correct answer from the database
 * @param studentAnswer The student's submitted answer
 * @returns boolean indicating if the answer is correct
 */
export function grade(answer: Answer, studentAnswer: StudentAnswer): boolean {
  // Handle case where answer type is not set (legacy data)
  // Use type assertion to access the type property
  const answerObj = answer as any;
  const answerType = answerObj.type || 'SINGLE_CHOICE';

  // Log the answer and student answer for debugging
  console.log('Grading answer:', {
    answerType,
    correctAnswer: {
      key: answerObj.key,
      textEn: answerObj.textEn,
      textZh: answerObj.textZh,
      answerSpec: answerObj.answerSpec
    },
    studentAnswer: {
      // These fields will be populated later if needed
      submittedKey: (studentAnswer as any).submittedKey,
      submittedText: (studentAnswer as any).submittedText,
      submittedJson: (studentAnswer as any).submittedJson
    }
  });

  // Check if the answer type is valid
  if (!answerType) {
    console.error('Invalid answer type:', answerType);
    return false;
  }

  // For debugging, log the raw answer object
  console.log('Raw answer object:', JSON.stringify(answer));

  let isCorrect = false;

  // Use type assertion for studentAnswer (answerObj is already defined above)
  const studentAnswerObj = studentAnswer as any;

  // Log the available answer fields
  console.log('Student answer fields:', {
    submittedKey: studentAnswerObj.submittedKey,
    submittedText: studentAnswerObj.submittedText,
    submittedJson: studentAnswerObj.submittedJson
  });

  switch (answerType) {
    case 'SINGLE_CHOICE': {
      // Use submittedKey for single choice questions
      const studentKey = studentAnswerObj.submittedKey;
      isCorrect = studentKey === answerObj.key;
      console.log(`SINGLE_CHOICE: ${studentKey} === ${answerObj.key} => ${isCorrect}`);
      return isCorrect;
    }

    case 'MULTI_CHOICE': {
      if (!answerObj.answerSpec) {
        console.log('MULTI_CHOICE: Missing answerSpec');
        return false;
      }

      try {
        const expectedKeys = answerObj.answerSpec.keys.sort();

        // Try to get submittedJson, or parse submittedAnswer if it's a JSON string
        let submittedJson = studentAnswerObj.submittedJson;
        if (!submittedJson && studentAnswerObj.submittedAnswer) {
          try {
            submittedJson = JSON.parse(studentAnswerObj.submittedAnswer);
          } catch (e) {
            console.error('Error parsing submittedAnswer as JSON:', e);
          }
        }

        if (!submittedJson || !submittedJson.keys) {
          console.log('MULTI_CHOICE: Missing submittedJson or keys');
          return false;
        }

        const submittedKeys = submittedJson.keys.sort();

        // Compare arrays by converting to strings
        isCorrect = JSON.stringify(expectedKeys) === JSON.stringify(submittedKeys);
        console.log(`MULTI_CHOICE: ${JSON.stringify(submittedKeys)} === ${JSON.stringify(expectedKeys)} => ${isCorrect}`);
        return isCorrect;
      } catch (error) {
        console.error('Error grading MULTI_CHOICE answer:', error);
        return false;
      }
    }

    case 'SHORT_TEXT': {
      // Get the appropriate text based on language
      const answerText = answerObj.textEn || answerObj.textZh || answerObj.textMs || '';
      // Use submittedText for short text questions
      const submittedText = studentAnswerObj.submittedText || '';

      // Case-insensitive comparison with trimming
      isCorrect = answerText.trim().toLowerCase() === submittedText.trim().toLowerCase();
      console.log(`SHORT_TEXT: "${submittedText}" === "${answerText}" => ${isCorrect}`);
      return isCorrect;
    }

    case 'TRUE_FALSE': {
      // Use submittedKey for true/false questions
      const studentKey = studentAnswerObj.submittedKey;
      isCorrect = studentKey === answerObj.key;
      console.log(`TRUE_FALSE: ${studentKey} === ${answerObj.key} => ${isCorrect}`);
      return isCorrect;
    }

    case 'FILL_IN_THE_BLANK': {
      // For fill in the blank, we can either use the text fields or answerSpec
      if (answerObj.answerSpec) {
        // If we have answerSpec, use it for more complex validation
        try {
          const acceptableAnswers = answerObj.answerSpec.acceptableAnswers || [];
          // Use submittedText for fill in the blank questions
          const submittedText = studentAnswerObj.submittedText || '';

          // Check if the submitted text matches any acceptable answer
          isCorrect = acceptableAnswers.some((acceptable: string) =>
            acceptable.trim().toLowerCase() === submittedText.trim().toLowerCase()
          );
          console.log(`FILL_IN_THE_BLANK (spec): "${submittedText}" in ${JSON.stringify(acceptableAnswers)} => ${isCorrect}`);
          return isCorrect;
        } catch (error) {
          console.error('Error grading FILL_IN_THE_BLANK answer:', error);
          return false;
        }
      } else {
        // Fall back to simple text comparison
        const answerText = answerObj.textEn || answerObj.textZh || answerObj.textMs || '';
        // Use submittedText for fill in the blank questions
        const submittedText = studentAnswerObj.submittedText || '';

        isCorrect = answerText.trim().toLowerCase() === submittedText.trim().toLowerCase();
        console.log(`FILL_IN_THE_BLANK (text): "${submittedText}" === "${answerText}" => ${isCorrect}`);
        return isCorrect;
      }
    }

    case 'MATCHING': {
      if (!answerObj.answerSpec) {
        console.log('MATCHING: Missing answerSpec');
        return false;
      }

      try {
        // For matching, we expect pairs of items
        const expectedPairs = answerObj.answerSpec.pairs;

        // Try to get submittedJson, or parse submittedAnswer if it's a JSON string
        let submittedJson = studentAnswerObj.submittedJson;
        if (!submittedJson && studentAnswerObj.submittedAnswer) {
          try {
            submittedJson = JSON.parse(studentAnswerObj.submittedAnswer);
          } catch (e) {
            console.error('Error parsing submittedAnswer as JSON:', e);
          }
        }

        if (!submittedJson || !submittedJson.pairs) {
          console.log('MATCHING: Missing submittedJson or pairs');
          return false;
        }

        const submittedPairs = submittedJson.pairs;

        // Deep equality check on the pairs
        isCorrect = JSON.stringify(expectedPairs) === JSON.stringify(submittedPairs);
        console.log(`MATCHING: ${JSON.stringify(submittedPairs)} === ${JSON.stringify(expectedPairs)} => ${isCorrect}`);
        return isCorrect;
      } catch (error) {
        console.error('Error grading MATCHING answer:', error);
        return false;
      }
    }

    case 'SEQUENCING': {
      if (!answerObj.answerSpec) {
        console.log('SEQUENCING: Missing answerSpec');
        return false;
      }

      try {
        // For sequencing, we expect an ordered array
        const expectedOrder = answerObj.answerSpec.order;

        // Try to get submittedJson, or parse submittedAnswer if it's a JSON string
        let submittedJson = studentAnswerObj.submittedJson;
        if (!submittedJson && studentAnswerObj.submittedAnswer) {
          try {
            submittedJson = JSON.parse(studentAnswerObj.submittedAnswer);
          } catch (e) {
            console.error('Error parsing submittedAnswer as JSON:', e);
          }
        }

        if (!submittedJson || !submittedJson.order) {
          console.log('SEQUENCING: Missing submittedJson or order');
          return false;
        }

        const submittedOrder = submittedJson.order;

        // Compare the arrays
        isCorrect = JSON.stringify(expectedOrder) === JSON.stringify(submittedOrder);
        console.log(`SEQUENCING: ${JSON.stringify(submittedOrder)} === ${JSON.stringify(expectedOrder)} => ${isCorrect}`);
        return isCorrect;
      } catch (error) {
        console.error('Error grading SEQUENCING answer:', error);
        return false;
      }
    }

    case 'LONG_TEXT_RUBRIC': {
      // Long text with rubric requires manual grading or AI grading
      // This would typically be handled elsewhere
      console.log('LONG_TEXT_RUBRIC: Requires manual or AI grading');
      return false;
    }

    default: {
      console.warn(`Unknown answer type: ${answerType}`);
      return false;
    }
  }
}
