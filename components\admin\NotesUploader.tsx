import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import ReactMarkdown from 'react-markdown';

interface Year {
  id: number;
  yearNumber: number;
}

interface Subject {
  id: number;
  name: string;
}

interface Unit {
  id: number;
  unitNumber: number;
  topicEn: string;
  topicZh: string;
}

interface ToastProps {
  type: 'success' | 'error';
  message: string;
}

const Toast: React.FC<ToastProps> = ({ type, message }) => {
  return (
    <div className={`fixed top-4 right-4 p-4 rounded-md shadow-md ${
      type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }`}>
      <div className="flex items-center">
        <span className="mr-2">
          {type === 'success' ? '✅' : '❌'}
        </span>
        {message}
      </div>
    </div>
  );
};

type UploadMethod = 'file' | 'markdown';

const NotesUploader: React.FC = () => {
  const { data: session, status } = useSession();
  const [years, setYears] = useState<Year[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedYearId, setSelectedYearId] = useState<number | ''>('');
  const [selectedSubjectId, setSelectedSubjectId] = useState<number | ''>('');
  const [selectedUnitId, setSelectedUnitId] = useState<number | ''>('');
  const [file, setFile] = useState<File | null>(null);
  const [markdownText, setMarkdownText] = useState<string>('');
  const [uploadMethod, setUploadMethod] = useState<UploadMethod>('file');
  const [noteKind, setNoteKind] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [toast, setToast] = useState<ToastProps | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const showToast = (type: 'success' | 'error', message: string) => {
    setToast({ type, message });
    setTimeout(() => {
      setToast(null);
    }, 3000);
  };

  // Check if user is admin
  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  if (status === 'unauthenticated' || session?.user?.role !== 'ADMIN') {
    return null;
  }

  // Fetch years on component mount
  useEffect(() => {
    const fetchYears = async () => {
      try {
        const response = await fetch('/api/years');
        if (!response.ok) {
          throw new Error('Failed to fetch years');
        }
        const data = await response.json();
        setYears(data);
      } catch (error) {
        console.error('Error fetching years:', error);
        showToast('error', 'Failed to fetch years');
      }
    };

    fetchYears();
  }, []);

  // Fetch subjects when year changes
  useEffect(() => {
    if (!selectedYearId) {
      setSubjects([]);
      return;
    }

    const fetchSubjects = async () => {
      try {
        const response = await fetch('/api/subjects');
        if (!response.ok) {
          throw new Error('Failed to fetch subjects');
        }
        const data = await response.json();
        setSubjects(data);
      } catch (error) {
        console.error('Error fetching subjects:', error);
        showToast('error', 'Failed to fetch subjects');
      }
    };

    fetchSubjects();
  }, [selectedYearId]);

  // Fetch units when subject changes
  useEffect(() => {
    if (!selectedSubjectId) {
      setUnits([]);
      return;
    }

    const fetchUnits = async () => {
      try {
        const response = await fetch(`/api/topics?subjectId=${selectedSubjectId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch units');
        }
        const data = await response.json();
        setUnits(data);
      } catch (error) {
        console.error('Error fetching units:', error);
        showToast('error', 'Failed to fetch units');
      }
    };

    fetchUnits();
  }, [selectedSubjectId]);

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedYearId(value ? parseInt(value) : '');
    setSelectedSubjectId('');
    setSelectedUnitId('');
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedSubjectId(value ? parseInt(value) : '');
    setSelectedUnitId('');
  };

  const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedUnitId(value ? parseInt(value) : '');
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleMarkdownChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMarkdownText(e.target.value);
  };

  const togglePreview = () => {
    setShowPreview(!showPreview);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedYearId || !selectedSubjectId || !selectedUnitId) {
      showToast('error', 'Please select Year, Subject, and Unit');
      return;
    }

    if (uploadMethod === 'file' && !file) {
      showToast('error', 'Please select a file to upload');
      return;
    }

    if (uploadMethod === 'markdown' && !markdownText.trim()) {
      showToast('error', 'Please enter markdown content');
      return;
    }

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('yearId', selectedYearId.toString());
      formData.append('subjectId', selectedSubjectId.toString());
      formData.append('unitId', selectedUnitId.toString());
      formData.append('uploadMethod', uploadMethod);

      // Add the kind parameter if selected
      if (noteKind) {
        formData.append('kind', noteKind);
      }

      if (uploadMethod === 'file' && file) {
        formData.append('file', file);
      } else if (uploadMethod === 'markdown') {
        formData.append('markdownText', markdownText);
      }

      const response = await fetch('/api/admin/upload-note', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to upload note');
      }

      const data = await response.json();

      // Reset only the file/markdown content and note kind, but preserve year, subject, and unit selections
      setFile(null);
      setMarkdownText('');
      setNoteKind('');
      setShowPreview(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      showToast('success', 'Note uploaded successfully');
    } catch (error) {
      console.error('Error uploading note:', error);
      showToast('error', error instanceof Error ? error.message : 'Failed to upload note');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-bold mb-4">Upload Study Notes</h2>

      {/* Upload Method Tabs */}
      <div className="flex border-b mb-4">
        <button
          className={`py-2 px-4 font-medium ${
            uploadMethod === 'file'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setUploadMethod('file')}
        >
          Upload File
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            uploadMethod === 'markdown'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setUploadMethod('markdown')}
        >
          Paste Markdown
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
          <select
            value={selectedYearId}
            onChange={handleYearChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
          >
            <option value="">Select Year</option>
            {years.map((year) => (
              <option key={year.id} value={year.id}>
                Year {year.yearNumber}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
          <select
            value={selectedSubjectId}
            onChange={handleSubjectChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
            disabled={!selectedYearId}
          >
            <option value="">Select Subject</option>
            {subjects.map((subject) => (
              <option key={subject.id} value={subject.id}>
                {subject.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
          <select
            value={selectedUnitId}
            onChange={handleUnitChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            required
            disabled={!selectedSubjectId}
          >
            <option value="">Select Unit</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.id}>
                Unit {unit.unitNumber}: {unit.topicEn}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Note Type</label>
          <div className="flex space-x-4 mt-1">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="noteKind"
                value=""
                checked={noteKind === ''}
                onChange={() => setNoteKind('')}
                className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Regular Note</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="noteKind"
                value="SAMPLE"
                checked={noteKind === 'SAMPLE'}
                onChange={() => setNoteKind('SAMPLE')}
                className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Sample Questions</span>
            </label>
          </div>
          <p className="mt-1 text-sm text-gray-500">
            Select "Sample Questions" if this material contains example questions for students.
          </p>
        </div>

        {uploadMethod === 'file' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">File</label>
            <input
              type="file"
              accept=".txt,.pdf,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
              ref={fileInputRef}
            />
            <div className="mt-2 text-sm text-gray-500">
              <p>Accepted file types:</p>
              <ul className="list-disc pl-5 mt-1 space-y-1">
                <li><span className="font-medium">PDF</span> - For documents and study materials</li>
                <li><span className="font-medium">Images</span> - JPG, JPEG, PNG for diagrams and visual content</li>
                <li><span className="font-medium">Text</span> - TXT files for plain text content</li>
              </ul>
            </div>
          </div>
        )}

        {uploadMethod === 'markdown' && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <label className="block text-sm font-medium text-gray-700">Markdown Content</label>
              <button
                type="button"
                onClick={togglePreview}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                {showPreview ? 'Edit' : 'Preview'}
              </button>
            </div>

            {!showPreview ? (
              <textarea
                value={markdownText}
                onChange={handleMarkdownChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows={10}
                required
                placeholder="# Your markdown content here..."
              />
            ) : (
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 prose prose-sm max-w-none h-64 overflow-y-auto">
                <ReactMarkdown>{markdownText}</ReactMarkdown>
              </div>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Enter markdown content for the study notes
            </p>
          </div>
        )}

        <div>
          <button
            type="submit"
            className={`w-full px-4 py-2 text-white font-medium rounded-md ${
              loading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            }`}
            disabled={loading}
          >
            {loading ? 'Uploading...' : 'Upload Note'}
          </button>
        </div>
      </form>

      {toast && <Toast type={toast.type} message={toast.message} />}
    </div>
  );
};

export default NotesUploader;
