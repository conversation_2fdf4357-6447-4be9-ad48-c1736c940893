import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';

interface Note {
  id: number;
  filename: string;
  fileUrl: string;
  mimeType: string;
  fileSize: number;
  contentType: string;
  yearId: number;
  subjectId: number;
  unitId: number;
  createdAt: string;
  year: {
    id: number;
    yearNumber: number;
  };
  subject: {
    id: number;
    name: string;
  };
  unit: {
    id: number;
    unitNumber: number;
    topicEn: string;
    topicZh: string;
  };
  content?: string;
}

interface NoteModalProps {
  note: Note;
  onClose: () => void;
}

const NoteModal: React.FC<NoteModalProps> = ({ note, onClose }) => {
  // State for loading content
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadError, setLoadError] = React.useState<string | null>(null);

  // Determine the content type to display
  const isMarkdown = note.contentType === 'MARKDOWN' || note.contentType === 'SAMPLE';
  const isPdf = note.mimeType === 'application/pdf';
  const isImage = note.mimeType.startsWith('image/');

  // Try to load content if it's not available
  React.useEffect(() => {
    const loadContent = async () => {
      if (isMarkdown && !note.content) {
        setIsLoading(true);
        setLoadError(null);

        try {
          // Try to fetch from our API
          const response = await fetch(`/api/admin/note-content/${note.id}`);

          if (response.ok) {
            const data = await response.json();
            note.content = data.content;
          } else {
            console.error(`Failed to load content from API: HTTP ${response.status}`);

            // Fallback: try to fetch directly from the URL
            try {
              // Check if the URL is a relative path or absolute URL
              const isRelativePath = !note.fileUrl.startsWith('http://') && !note.fileUrl.startsWith('https://');

              if (isRelativePath) {
                // For relative paths, use the current origin
                const origin = window.location.origin;
                const fullUrl = `${origin}${note.fileUrl.startsWith('/') ? note.fileUrl : `/${note.fileUrl}`}`;

                console.log(`Fallback: Fetching from relative path: ${fullUrl}`);
                const directResponse = await fetch(fullUrl);

                if (directResponse.ok) {
                  const content = await directResponse.text();
                  note.content = content;
                } else {
                  throw new Error(`HTTP ${directResponse.status}`);
                }
              } else {
                // For absolute URLs, fetch directly
                console.log(`Fallback: Fetching from absolute URL: ${note.fileUrl}`);
                const directResponse = await fetch(note.fileUrl);

                if (directResponse.ok) {
                  const content = await directResponse.text();
                  note.content = content;
                } else {
                  throw new Error(`HTTP ${directResponse.status}`);
                }
              }
            } catch (directError) {
              setLoadError(`Failed to load content: ${directError instanceof Error ? directError.message : String(directError)}`);
            }
          }
        } catch (error) {
          setLoadError(`Error loading content: ${error instanceof Error ? error.message : String(error)}`);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadContent();
  }, [note.id, isMarkdown, note.content, note.fileUrl]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={onClose}>
      <div
        className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-bold">
            {note.filename}
            <span className="ml-2 text-sm font-normal text-gray-500">
              ({note.year.yearNumber} | {note.subject.name} | Unit {note.unit.unitNumber})
            </span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {isMarkdown && (
            <div className="prose prose-sm sm:prose lg:prose-lg max-w-none markdown-content">
              <style jsx global>{`
                .markdown-content p {
                  margin-bottom: 1em;
                  white-space: pre-wrap;
                }
                .markdown-content br {
                  display: block;
                  content: "";
                  margin-top: 0.5em;
                }
                .markdown-content li {
                  white-space: pre-wrap;
                }
                .markdown-content pre {
                  white-space: pre-wrap;
                  overflow-x: auto;
                }
                .markdown-content code {
                  white-space: pre-wrap;
                }
              `}</style>
              {isLoading ? (
                <div className="flex justify-center items-center p-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                  <span className="ml-3 text-gray-600">Loading content...</span>
                </div>
              ) : note.content ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkBreaks]}
                  components={{
                    p: ({node, ...props}) => <p className="whitespace-pre-wrap" {...props} />,
                    li: ({node, ...props}) => <li className="whitespace-pre-wrap" {...props} />,
                    pre: ({node, ...props}) => <pre className="whitespace-pre-wrap overflow-x-auto" {...props} />,
                    code: ({node, ...props}) => <code className="whitespace-pre-wrap" {...props} />
                  }}
                >
                  {note.content}
                </ReactMarkdown>
              ) : loadError ? (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-700">
                    Error loading content: {loadError}
                  </p>
                  <p className="text-sm text-red-600 mt-2">
                    You can try to view the file directly:
                    <a
                      href={note.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline ml-2"
                    >
                      Open markdown file
                    </a>
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-yellow-700">
                    No content available. The markdown content could not be loaded.
                  </p>
                  <p className="text-sm text-yellow-600 mt-2">
                    You can try to view the file directly:
                    <a
                      href={note.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline ml-2"
                    >
                      Open markdown file
                    </a>
                  </p>
                </div>
              )}
            </div>
          )}

          {isPdf && (
            <div className="w-full h-[70vh]">
              <embed
                src={note.fileUrl}
                type="application/pdf"
                className="w-full h-full"
              />
            </div>
          )}

          {isImage && (
            <div className="flex items-center justify-center">
              <img
                src={note.fileUrl}
                alt={note.filename}
                className="max-h-[70vh] max-w-full object-contain"
              />
            </div>
          )}

          {!isMarkdown && !isPdf && !isImage && (
            <div className="text-center p-8">
              <p className="text-gray-500">
                This file type cannot be previewed.
                <a
                  href={note.fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline ml-2"
                >
                  Download file
                </a>
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t flex justify-between items-center text-sm text-gray-500">
          <div>
            {new Date(note.createdAt).toLocaleString()}
          </div>
          <div>
            {(note.fileSize / 1024).toFixed(2)} KB • {note.contentType}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoteModal;
