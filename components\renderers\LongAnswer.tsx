import React, { useState, useEffect } from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';

const LongAnswer: React.FC<BaseRendererProps<'LONG_ANSWER'>> = ({
  promptEn,
  promptZh,
  originalPrompt,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp
}) => {
  // Get the original language from the parent component
  const isChineseOriginal = originalPrompt === promptZh;
  const [wordCount, setWordCount] = useState(0);

  // Calculate word count when answer changes
  useEffect(() => {
    if (selectedAnswer) {
      const words = selectedAnswer.trim().split(/\s+/);
      setWordCount(words.length);
    } else {
      setWordCount(0);
    }
  }, [selectedAnswer]);

  const maxWords = spec?.maxWords;
  const isOverLimit = maxWords ? wordCount > maxWords : false;

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      {spec && (
        <div className="mb-4">
          <div className="text-lg font-medium mb-4">
            {spec.prompt}
          </div>

          <div className="mb-2">
            <textarea
              value={selectedAnswer}
              onChange={e => onAnswerChange(e.target.value)}
              placeholder={isChineseOriginal ? '在此处写下您的答案...' : 'Write your answer here...'}
              className={`w-full text-black rounded p-2 h-32 ${isOverLimit ? 'border-2 border-red-500' : ''}`}
            />

            <div className={`text-sm mt-1 flex justify-between ${isOverLimit ? 'text-red-500' : 'text-gray-300'}`}>
              <div>
                {isChineseOriginal ? '字数: ' : 'Word count: '}{wordCount}
                {maxWords && ` / ${maxWords}`}
              </div>

              {isOverLimit && (
                <div className="text-red-500">
                  {isChineseOriginal ? '超过最大字数' : 'Exceeds maximum word count'}
                </div>
              )}
            </div>
          </div>

          {spec.keyPoints && spec.keyPoints.length > 0 && (
            <div className="mt-4 bg-blue-100 p-3 rounded text-blue-800">
              <div className="font-medium mb-2">
                {isChineseOriginal ? '需要解决的要点:' : 'Key points to address:'}
              </div>
              <ul className="list-disc pl-5">
                {spec.keyPoints.map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </RendererWrapper>
  );
};

export default LongAnswer;
