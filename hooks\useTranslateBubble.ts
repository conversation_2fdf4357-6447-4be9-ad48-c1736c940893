import { useState, useEffect } from 'react';
import { Question } from '../types/quiz';
import { useSession } from 'next-auth/react';

interface TranslationMenu {
  x: number;
  y: number;
  text: string;
  originalText?: string;
}

export function useTranslateBubble(enabled: boolean, currentQuestion?: Question) {
  const { data: session } = useSession();

  // Create a fallback context with default values
  const quizContext = {
    addAiTutorMessage: (_sender: 'ai' | 'student', content: string) => {
      console.log('Translation would be added to AI tutor chat:', content);
    },
    displayLanguage: 'en' as 'en' | 'zh' | 'ms',
    setIsAiTutorCollapsed: (collapsed: boolean) => {
      console.log('AI tutor panel would be', collapsed ? 'collapsed' : 'expanded');
    }
  };

  const { addAiTutorMessage, displayLanguage } = quizContext;
  const [menu, setMenu] = useState<TranslationMenu | null>(null);

  const onMouseUp = (_: MouseEvent) => {
    if (!enabled) return;

    const sel = window.getSelection();
    const text = sel?.toString().trim();

    if (text) {
      const rect = sel!.getRangeAt(0).getBoundingClientRect();
      setMenu({
        x: rect.left + rect.width/2,
        y: rect.top - 10,
        text,
        originalText: text // Store the original text for reference
      });
    } else {
      setMenu(null);
    }
  };

  useEffect(() => {
    document.addEventListener('mouseup', onMouseUp);
    return () => document.removeEventListener('mouseup', onMouseUp);
  }, [enabled]);

  async function handleTranslate() {
    if (!menu) return;

    // Set text to "Translating..." to indicate loading
    setMenu(prev => prev ? { ...prev, text: 'Translating...' } : null);

    try {
      const res = await fetch('/api/ai-translator', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
          highlightedText: menu.text === 'Translating...' ? menu.originalText : menu.text,
          question: currentQuestion ? (currentQuestion.promptEn || currentQuestion.promptZh || '') : 'Context for translation'
        })
      });

      if (!res.ok) {
        throw new Error(`Translation failed: ${res.statusText}`);
      }

      const data = await res.json();
      const translatedText = data.translatedText;

      // Log debug information if available
      if (data.debug) {
        console.log('Translation debug info:', data.debug);
      }

      // Create and show toast with improved styling
      const div = document.createElement('div');

      // Create a container with flex layout
      div.className = 'fixed bottom-20 inset-x-4 md:inset-x-1/4 bg-black/90 text-white p-3 rounded-lg text-center z-50 shadow-lg flex items-center justify-center';

      // Create an icon element
      const icon = document.createElement('span');
      icon.textContent = '🌐';
      icon.className = 'mr-2';

      // Create a text element
      const text = document.createElement('span');
      text.textContent = translatedText;

      // Add the elements to the container
      div.appendChild(icon);
      div.appendChild(text);

      document.body.appendChild(div);

      // Dispatch a custom event with the translation result
      console.log('🚀 Creating translationComplete event with:', {
        translatedText,
        originalText: menu.originalText || menu.text
      });

      const translationCompleteEvent = new CustomEvent('translationComplete', {
        detail: {
          translatedText,
          originalText: menu.originalText || menu.text
        }
      });

      console.log('🚀 Dispatching translationComplete event:', translationCompleteEvent);
      window.dispatchEvent(translationCompleteEvent);
      console.log('🚀 translationComplete event dispatched');

      // DIRECT TEST: Try to manually add the message to the AI tutor chat
      try {
        // Create translation message
        const translationPrefix = 'Translation of "';
        const translationSuffix = '": ';
        const message = `${translationPrefix}${menu.originalText || menu.text}${translationSuffix}${translatedText}`;

        // Try to access the global window object to call a global function
        console.log('🔍 Attempting direct AI tutor message addition');
        if (typeof window !== 'undefined' && window.document) {
          // Create and dispatch a more specific event that might be easier to catch
          const directEvent = new CustomEvent('directTranslationMessage', {
            detail: {
              message,
              sender: 'ai',
              translatedText,
              originalText: menu.originalText || menu.text
            }
          });
          console.log('📣 Dispatching directTranslationMessage event');
          window.dispatchEvent(directEvent);

          // Set a global variable that can be checked elsewhere
          (window as any).__lastTranslation = {
            message,
            sender: 'ai',
            translatedText,
            originalText: menu.originalText || menu.text,
            timestamp: new Date().toISOString()
          };
          console.log('🌐 Set window.__lastTranslation:', (window as any).__lastTranslation);

          // Try to use the global function if available
          if (typeof (window as any).__addAiTutorMessage === 'function') {
            console.log('🌐 Found global __addAiTutorMessage function, calling it directly');
            (window as any).__addAiTutorMessage('ai', message);
          } else {
            console.log('🌐 Global __addAiTutorMessage function not found');
          }
        }
      } catch (error) {
        console.error('❌ Error in direct test:', error);
      }

      // Add the translation to the AI tutor chat directly
      if (typeof addAiTutorMessage === 'function') {
        try {
          // Create translation message based on the current display language
          const translationPrefix = displayLanguage === 'en'
            ? 'Translation of "'
            : displayLanguage === 'ms'
              ? 'Terjemahan untuk "'
              : '翻译 "';

          const translationSuffix = displayLanguage === 'en'
            ? '": '
            : displayLanguage === 'ms'
              ? '": '
              : '": ';

          const message = `${translationPrefix}${menu.originalText || menu.text}${translationSuffix}${translatedText}`;

          // Add the message to the AI tutor chat
          addAiTutorMessage('ai', message);

          // Don't automatically expand the AI tutor panel
          // Instead, we'll let the notification system handle this
          // The translation will be visible when the user opens the AI tutor

          console.log('Translation added to AI tutor chat:', message);
        } catch (error) {
          console.error('Error adding translation to AI tutor chat:', error);
        }
      } else {
        console.log('addAiTutorMessage function not available');
      }

      // Log the translation
      try {
        await fetch('/api/log-translation', {
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
          body: JSON.stringify({
            childId: session?.user?.id ? Number(session.user.id) : 1, // Get childId from auth context with fallback
            translatedText: menu.originalText || menu.text,
            questionId: currentQuestion?.id || 0
          })
        });
      } catch (logError) {
        console.error('Error logging translation:', logError);
      }

      // Remove toast after delay
      setTimeout(() => div.remove(), 3000);

      // Clear menu
      setMenu(null);
    } catch (error) {
      console.error('Translation error:', error);

      // Show error toast with improved styling
      const div = document.createElement('div');

      // Create a container with flex layout
      div.className = 'fixed bottom-20 inset-x-4 md:inset-x-1/4 bg-red-600/90 text-white p-3 rounded-lg text-center z-50 shadow-lg flex items-center justify-center';

      // Create an icon element
      const icon = document.createElement('span');
      icon.textContent = '⚠️';
      icon.className = 'mr-2';

      // Create a text element
      const text = document.createElement('span');
      text.textContent = 'Translation failed. Please try again.';

      // Add the elements to the container
      div.appendChild(icon);
      div.appendChild(text);

      document.body.appendChild(div);

      // Remove toast after delay
      setTimeout(() => div.remove(), 3000);

      // Reset menu to original state
      setMenu(prev => prev ? {
        ...prev,
        text: prev.originalText || prev.text
      } : null);
    }
  }

  return {
    isVisible: !!menu,
    position: menu ? { top: menu.y, left: menu.x } : null,
    text: menu?.text || '',
    originalText: menu?.originalText || '',
    handleTranslate,
    handleClose: () => setMenu(null)
  };
}

