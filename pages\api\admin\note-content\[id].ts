import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import prisma from '../../../../lib/prisma';
import fs from 'fs/promises';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Get note ID from the URL
    const { id } = req.query;

    if (!id || Array.isArray(id)) {
      return res.status(400).json({ message: 'Invalid note ID' });
    }

    // Fetch the note
    const note = await prisma.note.findUnique({
      where: { id: parseInt(id) },
    });

    if (!note) {
      return res.status(404).json({ message: 'Note not found' });
    }

    // Only proceed for markdown or sample notes
    if (note.contentType !== 'MARKDOWN' && note.contentType !== 'SAMPLE') {
      return res.status(400).json({ message: 'Note is not a markdown or sample note' });
    }

    let content;

    // Check if the URL is a relative path or absolute URL
    const isRelativePath = !note.fileUrl.startsWith('http://') && !note.fileUrl.startsWith('https://');

    if (isRelativePath) {
      // For relative paths, try to read the file directly from the uploads directory
      try {
        console.log(`Trying to read file from uploads directory: ${note.fileUrl}`);

        // Construct the full path to the file
        const fullPath = path.join(process.cwd(), note.fileUrl.startsWith('/') ? note.fileUrl.substring(1) : note.fileUrl);
        console.log(`Full path: ${fullPath}`);

        // Read the file
        content = await fs.readFile(fullPath, 'utf-8');
        console.log(`Successfully read file from disk: ${fullPath}`);
      } catch (fileError) {
        console.error(`Failed to read file directly: ${fileError}`);

        // Try to fetch from the API endpoint
        try {
          // Construct the full URL using the host from the request
          const host = req.headers.host || 'localhost:3000';
          const protocol = host.includes('localhost') ? 'http' : 'https';
          const apiUrl = `${protocol}://${host}${note.fileUrl}`;

          console.log(`Trying to fetch from API: ${apiUrl}`);
          const response = await fetch(apiUrl);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }

          content = await response.text();
          console.log(`Successfully fetched from API, content length: ${content.length}`);
        } catch (apiError) {
          console.error(`Failed to fetch from API: ${apiError}`);
          return res.status(500).json({
            message: `Failed to load content: ${fileError}. API fetch error: ${apiError}`
          });
        }
      }
    } else {
      // For absolute URLs, try to fetch directly
      try {
        console.log(`Fetching from absolute URL: ${note.fileUrl}`);
        const response = await fetch(note.fileUrl);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        content = await response.text();
        console.log(`Successfully fetched from URL, content length: ${content.length}`);
      } catch (fetchError) {
        console.error(`Failed to fetch from URL: ${fetchError}`);
        return res.status(500).json({
          message: `Failed to load content: ${fetchError}`
        });
      }
    }

    // Return the content
    return res.status(200).json({ content });
  } catch (error) {
    console.error('Error fetching note content:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
