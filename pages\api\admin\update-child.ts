import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { Language } from '@prisma/client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check authentication and admin role
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (session.user?.role !== 'ADMIN') {
    return res.status(403).json({ message: 'Forbidden - Admin access required' });
  }

  try {
    const { childId, name, year, quizLanguage, menuLanguage, showDualLanguage } = req.body;

    // Validate request body
    if (!childId) {
      return res.status(400).json({ message: 'Child ID is required' });
    }

    // Find the child
    const child = await prisma.child.findUnique({
      where: { id: Number(childId) }
    });

    if (!child) {
      return res.status(404).json({ message: 'Child not found' });
    }

    // Prepare update data
    const updateData: any = {};

    if (name !== undefined) {
      updateData.name = name;
    }

    if (year !== undefined) {
      updateData.year = year;
    }

    if (quizLanguage !== undefined) {
      // Validate quiz language
      if (!Object.values(Language).includes(quizLanguage as Language)) {
        return res.status(400).json({ message: 'Invalid quiz language' });
      }
      updateData.quizLanguage = quizLanguage;
    }

    if (menuLanguage !== undefined) {
      // Validate menu language
      if (!Object.values(Language).includes(menuLanguage as Language)) {
        return res.status(400).json({ message: 'Invalid menu language' });
      }
      updateData.menuLanguage = menuLanguage;
    }

    if (showDualLanguage !== undefined) {
      updateData.showDualLanguage = Boolean(showDualLanguage);
    }

    // Update the child
    const updatedChild = await prisma.child.update({
      where: { id: Number(childId) },
      data: updateData
    });

    res.status(200).json({
      message: 'Child updated successfully',
      child: {
        id: updatedChild.id,
        name: updatedChild.name,
        year: updatedChild.year,
        username: updatedChild.username,
        quizLanguage: updatedChild.quizLanguage,
        menuLanguage: updatedChild.menuLanguage,
        showDualLanguage: updatedChild.showDualLanguage
      }
    });
  } catch (error) {
    console.error('Error updating child:', error);
    res.status(500).json({ message: 'Failed to update child. Please try again.' });
  }
}
