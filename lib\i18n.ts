import { Language, Question } from '@prisma/client';

/**
 * Picks the appropriate prompt text based on the question's original language
 * @param q The question object
 * @returns The prompt text in the original language
 */
export function pickPrompt(q: Question): string {
  switch (q.originalLanguage) {
    case Language.EN:
      return q.promptEn ?? '';
    case Language.MS:
      return q.promptMs ?? q.promptEn ?? '';
    default: // Default to Chinese
      return q.promptZh ?? '';
  }
}

/**
 * Picks the appropriate text from an answer or choice based on the specified language
 * @param item The answer or choice object with textEn, textZh, and possibly textMs properties
 * @param lang The language to pick
 * @returns The text in the specified language
 */
export function pickAnswerText(
  item: { textEn?: string; textZh?: string; textMs?: string },
  lang: Language
): string {
  switch (lang) {
    case Language.EN:
      return item.textEn ?? '';
    case Language.MS:
      return item.textMs ?? item.textEn ?? '';
    default: // Default to Chinese
      return item.textZh ?? '';
  }
}

/**
 * Picks the appropriate text from a topic based on the specified language
 * @param topic The topic object with topicEn, topicZh, and possibly topicMs properties
 * @param lang The language to pick
 * @returns The text in the specified language
 */
export function pickTopicText(
  topic: { topicEn?: string; topicZh?: string; topicMs?: string },
  lang: Language
): string {
  switch (lang) {
    case Language.EN:
      return topic.topicEn ?? '';
    case Language.MS:
      return topic.topicMs ?? topic.topicEn ?? '';
    default: // Default to Chinese
      return topic.topicZh ?? '';
  }
}

/**
 * Picks the appropriate text from a subtopic based on the specified language
 * @param question The question object with subTopicEn, subTopicZh, and possibly subTopicMs properties
 * @param lang The language to pick
 * @returns The text in the specified language
 */
export function pickSubTopicText(
  question: { subTopicEn?: string; subTopicZh?: string; subTopicMs?: string },
  lang: Language
): string {
  switch (lang) {
    case Language.EN:
      return question.subTopicEn ?? '';
    case Language.MS:
      return question.subTopicMs ?? question.subTopicEn ?? '';
    default: // Default to Chinese
      return question.subTopicZh ?? '';
  }
}

/**
 * Picks the appropriate text from an explanation based on the specified language
 * @param explanation The explanation object with textEn, textZh, and possibly textMs properties
 * @param lang The language to pick
 * @returns The text in the specified language
 */
export function pickExplanationText(
  explanation: { textEn?: string; textZh?: string; textMs?: string } | null,
  lang: Language
): string {
  if (!explanation) return '';

  switch (lang) {
    case Language.EN:
      return explanation.textEn ?? '';
    case Language.MS:
      return explanation.textMs ?? explanation.textEn ?? '';
    default: // Default to Chinese
      return explanation.textZh ?? '';
  }
}

/**
 * Picks the appropriate alt text from media based on the specified language
 * @param media The media object with altEn, altZh, and possibly altMs properties
 * @param lang The language to pick
 * @returns The alt text in the specified language
 */
export function pickMediaAltText(
  media: { altEn?: string; altZh?: string; altMs?: string } | null,
  lang: Language
): string {
  if (!media) return '';

  switch (lang) {
    case Language.EN:
      return media.altEn ?? '';
    case Language.MS:
      return media.altMs ?? media.altEn ?? '';
    default: // Default to Chinese
      return media.altZh ?? '';
  }
}
