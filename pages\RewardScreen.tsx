import React from 'react';
import { TrophyIcon, StarIcon } from 'lucide-react';
interface RewardScreenProps {
  earnedXp: number;
  onContinue: () => void;
}
export const RewardScreen: React.FC<RewardScreenProps> = ({
  earnedXp,
  onContinue
}) => {
  return <div className="flex-1 flex flex-col items-center justify-center p-4 bg-[#0F5FA6] text-white">
      <div className="text-6xl mb-6">🎉</div>
      <h2 className="text-3xl font-bold mb-2">Great Job!</h2>
      <p className="text-center mb-6">You've completed the lesson!</p>
      <div className="bg-[#0A8CBF] rounded-lg p-6 mb-8 w-full max-w-sm">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <TrophyIcon size={24} className="text-[#05DBF2] mr-2" />
            <span className="font-bold">XP Earned</span>
          </div>
          <span className="text-xl font-bold">+{earnedXp}</span>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <StarIcon size={24} className="text-[#05DBF2] mr-2" />
            <span className="font-bold">Streak Progress</span>
          </div>
          <div className="text-xl font-bold">+1 day</div>
        </div>
      </div>
      <button onClick={onContinue} className="px-8 py-4 bg-[#04B2D9] text-white font-bold rounded-lg shadow-lg hover:bg-[#05DBF2] transition-colors">
        Continue
      </button>
    </div>;
};