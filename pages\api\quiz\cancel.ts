import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
// Remove getSession import
// import { getSession } from 'next-auth/react';
import { getServerSession } from 'next-auth/next'; // Import recommended server-side utility
import { authOptions } from '../auth/[...nextauth]'; // Import your authOptions
import { QuizStatus } from '@prisma/client'; // Import QuizStatus enum

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Use getServerSession for server-side session retrieval
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { attemptId } = req.body;

  if (!attemptId) {
    return res.status(400).json({ message: 'Invalid attemptId' });
  }

  try {
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: {
        id: Number(attemptId),
      },
      select: {
        id: true,
        childId: true,
      },
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    // Verify that the logged-in user is the child who owns the quiz attempt
    if (session.user?.id !== quizAttempt.childId.toString()) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    // Update the quiz attempt status to CANCELED
    await prisma.quizAttempt.update({
      where: {
        id: Number(attemptId),
      },
      data: {
        status: QuizStatus.CANCELED,
      },
    });

    res.status(200).json({ message: 'Quiz attempt canceled successfully' });

  } catch (error) {
    console.error('Error canceling quiz attempt:', error);
    res.status(500).json({ message: 'Error canceling quiz attempt' });
  }
}
