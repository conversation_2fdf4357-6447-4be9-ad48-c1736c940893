import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

/**
 * API endpoint for fetching units for a specific subject and year
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { subjectId, yearNumber } = req.query;

  if (!subjectId || typeof subjectId !== 'string') {
    return res.status(400).json({ message: 'Subject ID is required' });
  }

  if (!yearNumber || typeof yearNumber !== 'string') {
    return res.status(400).json({ message: 'Year number is required' });
  }

  try {
    const subjectIdNum = parseInt(subjectId, 10);

    if (isNaN(subjectIdNum)) {
      return res.status(400).json({ message: 'Invalid Subject ID' });
    }

    // Convert year string (e.g., "Year 5") to number (e.g., 5)
    let yearNum: number;

    if (yearNumber.startsWith('Year ')) {
      const yearStr = yearNumber.replace('Year ', '');
      yearNum = parseInt(yearStr, 10);
    } else {
      yearNum = parseInt(yearNumber, 10);
    }

    if (isNaN(yearNum)) {
      return res.status(400).json({ message: 'Invalid year format' });
    }

    // Find the year record
    const year = await prisma.year.findUnique({
      where: {
        yearNumber: yearNum,
      },
    });

    if (!year) {
      return res.status(404).json({ message: 'Year not found' });
    }

    // Find all units for this subject and year
    const units = await prisma.unit.findMany({
      where: {
        subjectId: subjectIdNum,
        yearId: year.id,
      },
      orderBy: {
        unitNumber: 'asc',
      },
    });

    // In a real implementation, we would fetch the user's progress for each unit
    // For now, we'll just return the units with mock completion status and TP level
    const unitsWithProgress = units.map((unit, index) => {
      // Assign different TP levels to demonstrate the progress bar
      // First unit: TP6, second unit: TP4, third unit: TP2, rest: TP1
      let tpLevel = 1;
      if (index === 0) tpLevel = 6;
      else if (index === 1) tpLevel = 4;
      else if (index === 2) tpLevel = 2;

      return {
        id: unit.id,
        unitNumber: unit.unitNumber,
        name: unit.topicEn,
        nameZh: unit.topicZh,
        tpLevel: tpLevel,
        completed: true, // All units show as completed so we can display Practice button
        unlocked: true, // All units are unlocked
      };
    });

    res.status(200).json(unitsWithProgress);
  } catch (error) {
    console.error(`Error fetching units for subject ${subjectId} and year ${yearNumber}:`, error);
    res.status(500).json({ message: 'Failed to fetch units' });
  }
}
