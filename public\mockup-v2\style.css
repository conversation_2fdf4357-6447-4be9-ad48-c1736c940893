body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    margin: 0;
    background-color: #f0f4f8; /* Slightly bluish grey background */
    color: #333;
    padding-top: 20px;
}

.quiz-container {
    background-color: #ffffff;
    width: 100%;
    max-width: 400px;
    min-height: 80vh;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.quiz-header {
    display: flex;
    align-items: center;
    justify-content: center; /* Center progress bar as it's the only item */
    padding: 20px 20px; /* Adjusted padding */
    border-bottom: 2px solid #e0e0e0;
}

.progress-bar-container {
    flex-grow: 1; /* Allow it to take available width */
    max-width: 300px; /* Max width for progress bar */
    height: 15px;
    background-color: #e0e0e0; /* Lighter grey for track */
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 0%;
    background-color: #0A8CBF; /* New: Mid Blue for progress */
    border-radius: 10px;
    transition: width 0.3s ease-in-out;
}

.quiz-main {
    padding: 20px;
    flex-grow: 1;
}

#questionTitle {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #0F5FA6; /* New: Dark Blue for title */
}

.question-content {
    margin-bottom: 25px;
}

#questionText {
    font-size: 18px;
    line-height: 1.5;
    color: #2c3e50; /* Dark greyish blue for text */
}

.answer-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.option-btn {
    background-color: #ffffff;
    border: 2px solid #d0d9e0; /* Light blue-grey border */
    border-radius: 12px;
    padding: 15px;
    font-size: 16px;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
    width: 100%;
    box-sizing: border-box;
    color: #34495e; /* Darker text for options */
}

.option-btn:hover {
    background-color: #eaf6ff; /* Very light blue hover */
    border-color: #04B2D9; /* Mid-light blue border on hover */
}

.option-btn.selected {
    border-color: #04B2D9; /* New: Mid-light Blue border */
    background-color: #e0f7fa; /* New: Very Light Cyan/Blue (derived from #05DBF2) */
    color: #0F5FA6; /* Dark blue text when selected */
}

.option-btn.correct {
    border-color: #0F5FA6; /* New: Dark Blue border */
    background-color: #d6f5fd; /* New: Lighter Blue background (#05DBF2 made lighter) */
    color: #0F5FA6; /* New: Dark Blue text */
    font-weight: bold;
}

.option-btn.incorrect {
    border-color: #ff4b4b; /* Keep Red for incorrect */
    background-color: #ffdfe0;
    color: #c0392b; /* Darker red text */
    font-weight: bold;
}

.option-btn.show-correct {
    border-color: #0F5FA6 !important;
    background-color: #d6f5fd !important;
    color: #0F5FA6 !important;
}


.quiz-footer {
    /* No specific styles needed for the footer wrapper itself */
}

.feedback-message {
    padding: 15px 20px;
    font-size: 16px;
    font-weight: bold;
    text-align: left;
    display: none;
}

.feedback-message.correct {
    background-color: #e0f7fa; /* New: Very Light Cyan/Blue (same as selected option bg) */
    color: #0F5FA6; /* New: Dark Blue text */
    display: block;
}
.feedback-message.correct strong {
    color: #0A8CBF; /* Mid blue for "Correct!" part */
}

.feedback-message.incorrect {
    background-color: #ffeff0; /* Lighter Red */
    color: #c0392b; /* Dark Red text */
    display: block;
}
.feedback-message.incorrect strong {
    color: #e74c3c; /* Brighter red for "Incorrect." part */
}


.action-btn {
    width: calc(100% - 40px);
    margin: 20px;
    padding: 18px;
    font-size: 18px;
    font-weight: bold;
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    text-transform: uppercase;
    transition: background-color 0.2s, border-bottom-color 0.2s;
    border-bottom: 4px solid transparent;
}

.action-btn:disabled {
    background-color: #d0d9e0; /* Light blue-grey disabled */
    color: #7f8c8d; /* Muted text color */
    cursor: not-allowed;
    border-bottom-color: #b0bdc6;
}

.action-btn.active { /* CHECK button when active */
    background-color: #0A8CBF; /* New: Mid Blue */
    border-bottom-color: #0F5FA6; /* New: Dark Blue */
}
.action-btn.active:hover {
    background-color: #04B2D9; /* New: Lighter Mid Blue on hover */
}


.action-btn.continue { /* CONTINUE / RESTART button */
    background-color: #0A8CBF; /* New: Mid Blue */
    border-bottom-color: #0F5FA6; /* New: Dark Blue */
}
.action-btn.continue:hover {
    background-color: #04B2D9; /* New: Lighter Mid Blue on hover */
}

.action-btn.got-it { /* GOT IT button */
    background-color: #e74c3c; /* Standard Red */
    border-bottom-color: #c0392b; /* Darker Red */
}
.action-btn.got-it:hover {
    background-color: #ff6b54; /* Lighter Red on hover */
}