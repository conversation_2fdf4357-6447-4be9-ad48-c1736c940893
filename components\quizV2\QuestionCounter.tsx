import React from 'react';
import { useQuizV2 } from './QuizV2Provider';

export default function QuestionCounter() {
  const { currentIndex, questions, phase } = useQuizV2();

  // Calculate total questions
  const totalQuestions = questions.length || 1;

  return (
    <div className="text-base md:text-lg font-semibold text-gray-800 bg-gray-50 px-3 py-1 rounded-lg">
      {phase === 'REVIEW'
        ? `Review ${currentIndex + 1} of ${totalQuestions}`
        : `Question ${currentIndex + 1} of ${totalQuestions}`}
    </div>
  );
}
