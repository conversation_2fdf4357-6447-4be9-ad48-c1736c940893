import React from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';
import { Language } from '@prisma/client';
import HighlightedText from './HighlightedText';
import { useQuiz } from '../quiz/QuizContext';

export interface Choice {
  key: string;
  textEn: string;
  textZh: string;
  textMs?: string;
  mediaUrl?: string;
  mediaAlt?: string;
}

interface MultipleChoiceProps extends BaseRendererProps<'MULTIPLE_CHOICE'> {
  choices: Choice[];
  isCorrect?: boolean;
}

const MultipleChoice: React.FC<MultipleChoiceProps> = ({
  promptEn,
  promptZh,
  promptMs,
  originalPrompt,
  originalLanguage,
  promptMediaUrl,
  promptMediaAlt,
  choices,
  keywords,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  showKeywords,
  onMouseUp,
  spec,
  questionId,
  isIncorrect,
  submittedAnswer,
  isCorrect,
  disableAnswerSelection
}) => {
  // Get the handleNext function and childQuizLanguage from the QuizContext
  const { handleNext, childQuizLanguage } = useQuiz();

  // Get the language to use for choices based on the originalLanguage (which is now set to childQuizLanguage in QuestionRenderer)
  const useLanguage = originalLanguage || childQuizLanguage || 'EN';

  // Debug output
  console.log('MultipleChoice - childQuizLanguage:', childQuizLanguage);
  console.log('MultipleChoice - originalLanguage:', originalLanguage);
  console.log('MultipleChoice - useLanguage:', useLanguage);
  const handleSelect = (key: string) => {
    // Don't allow changing the answer if it's already correct and submitted
    if (disableAnswerSelection) {
      return;
    }
    onAnswerChange(key);
  };

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt ? (
          <HighlightedText
            text={originalPrompt}
            keywords={keywords}
            language={originalLanguage || Language.EN}
            showHighlights={showKeywords || false}
            className="question-text"
          />
        ) : displayLanguage === 'en' ? (
          <HighlightedText
            text={promptEn}
            keywords={keywords}
            language={Language.EN}
            showHighlights={showKeywords || false}
            className="question-text"
          />
        ) : displayLanguage === 'ms' && promptMs ? (
          <HighlightedText
            text={promptMs}
            keywords={keywords}
            language={Language.MS}
            showHighlights={showKeywords || false}
            className="question-text"
          />
        ) : (
          <HighlightedText
            text={promptZh}
            keywords={keywords}
            language={Language.ZH}
            showHighlights={showKeywords || false}
            className="question-text"
          />
        )}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="space-y-3">
        {choices.map(choice => {
          const isSelected = selectedAnswer === choice.key;

          // Determine background color based on selection and correctness
          let bg = 'bg-white text-black';
          if (isSelected) {
            // Only show red for incorrect answers if this is the submitted answer
            if (isIncorrect && selectedAnswer === submittedAnswer) {
              // If answer is selected, submitted, and incorrect, show red
              bg = 'bg-red-300';
            } else {
              // If answer is selected but not submitted or not incorrect, show blue
              bg = 'bg-blue-300';
            }
          }

          return (
            <div key={choice.key} className="choice-container border border-gray-300 rounded-lg">
              <button
                onClick={() => handleSelect(choice.key)}
                disabled={disableAnswerSelection}
                className={`${bg} w-full text-left px-4 py-3 flex items-center justify-between focus:outline-none ${disableAnswerSelection && !isSelected ? 'opacity-60 cursor-not-allowed' : ''}`}
              >
                <div>
                  <span
                    onMouseUp={(e) => {
                      // Don't handle selection if answer selection is disabled
                      if (disableAnswerSelection) {
                        return;
                      }

                      const selection = window.getSelection()?.toString().trim();
                      if (selection) {
                        e.stopPropagation();
                        if (onMouseUp) onMouseUp(e);
                      } else {
                        handleSelect(choice.key);
                      }
                    }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    {useLanguage === 'ZH'
                      ? choice.textZh
                      : useLanguage === 'MS' && choice.textMs
                        ? choice.textMs
                        : choice.textEn}
                  </span>
                </div>
                {isSelected && isIncorrect && selectedAnswer === submittedAnswer && (
                  <div className="text-red-700 font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    {displayLanguage === 'en' ? 'Incorrect' : displayLanguage === 'ms' ? 'Tidak betul' : '不正确'}
                  </div>
                )}
                {isSelected && isCorrect && selectedAnswer === submittedAnswer && (
                  <div className="text-green-700 font-bold flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {displayLanguage === 'en' ? 'Correct!' : displayLanguage === 'ms' ? 'Betul!' : '正确！'}
                  </div>
                )}
              </button>
            </div>
          );
        })}

      </div>
    </RendererWrapper>
  );
};

export default MultipleChoice;
