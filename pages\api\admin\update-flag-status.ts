import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Check if the user is an admin
  if (session.user?.role !== 'ADMIN') {
    return res.status(403).json({ message: 'Forbidden - Admin access required' });
  }

  const { flagId, status, reviewNotes } = req.body;

  if (!flagId || !status) {
    return res.status(400).json({ message: 'Flag ID and status are required' });
  }

  try {
    // Update the flag status
    const updatedFlag = await prisma.questionFlag.update({
      where: {
        id: Number(flagId),
      },
      data: {
        status,
        reviewNotes,
        reviewedAt: new Date(),
      },
    });

    return res.status(200).json({ 
      success: true, 
      message: 'Flag status updated successfully',
      flag: updatedFlag 
    });
  } catch (error) {
    console.error('Error updating flag status:', error);
    return res.status(500).json({ 
      success: false,
      message: 'An error occurred while updating flag status' 
    });
  }
}
