// pages/api/translate.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import dict from '../../data/dict.json';

type DictionaryType = {
  [key: string]: string;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { text } = req.body;
  if (!text) {
    return res.status(400).json({ message: 'Text is required' });
  }

  const dictionary: DictionaryType = dict;
  const translation = dictionary[text] || text;

  res.status(200).json({ translation });
}
