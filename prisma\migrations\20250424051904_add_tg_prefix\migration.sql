/*
  Warnings:

  - You are about to drop the `Subject` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Unit` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Year` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Unit" DROP CONSTRAINT "Unit_subjectId_fkey";

-- DropForeignKey
ALTER TABLE "Unit" DROP CONSTRAINT "Unit_yearId_fkey";

-- DropTable
DROP TABLE "Subject";

-- DropTable
DROP TABLE "Unit";

-- DropTable
DROP TABLE "Year";

-- CreateTable
CREATE TABLE "TG_Subject" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "TG_Subject_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Year" (
    "id" SERIAL NOT NULL,
    "yearNumber" INTEGER NOT NULL,

    CONSTRAINT "TG_Year_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Unit" (
    "id" SERIAL NOT NULL,
    "unitNumber" INTEGER NOT NULL,
    "topicEn" TEXT NOT NULL,
    "topicZh" TEXT NOT NULL,
    "subjectId" INTEGER NOT NULL,
    "yearId" INTEGER NOT NULL,

    CONSTRAINT "TG_Unit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TG_Subject_name_key" ON "TG_Subject"("name");

-- CreateIndex
CREATE UNIQUE INDEX "TG_Year_yearNumber_key" ON "TG_Year"("yearNumber");

-- CreateIndex
CREATE UNIQUE INDEX "TG_Unit_unitNumber_subjectId_yearId_key" ON "TG_Unit"("unitNumber", "subjectId", "yearId");

-- AddForeignKey
ALTER TABLE "TG_Unit" ADD CONSTRAINT "TG_Unit_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "TG_Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Unit" ADD CONSTRAINT "TG_Unit_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "TG_Year"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
