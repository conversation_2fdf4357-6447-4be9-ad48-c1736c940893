import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Verifying subjects in the database...');

    // Query all subjects
    const subjects = await prisma.subject.findMany({
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`Found ${subjects.length} subjects:`);
    
    // Display the subjects in a table format
    console.log('ID\tSubject Name');
    console.log('--\t------------');
    
    subjects.forEach(subject => {
      console.log(`${subject.id}\t${subject.name}`);
    });
  } catch (error) {
    console.error('Error verifying subjects:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  });
