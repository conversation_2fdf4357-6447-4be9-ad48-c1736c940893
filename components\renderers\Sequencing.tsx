import React, { useState, useEffect } from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';
import { Language } from '@prisma/client';
import HighlightedText from './HighlightedText';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// SortableItem component for each draggable item
interface SortableItemProps {
  id: number;
  index: number;
  itemIndex: number;
  spec: any;
  keywords?: any;
  originalLanguage?: Language;
  showKeywords?: boolean;
  isChineseOriginal: boolean;
  isMalayOriginal: boolean;
}

const SortableItem: React.FC<SortableItemProps> = ({
  id,
  index,
  itemIndex,
  spec,
  keywords,
  originalLanguage,
  showKeywords,
  isChineseOriginal,
  isMalayOriginal
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
    position: 'relative' as 'relative',
    cursor: 'grab'
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`flex items-center bg-white text-black rounded overflow-hidden ${isDragging ? 'shadow-lg' : ''}`}
    >
      <div className="bg-blue-500 text-white px-3 py-3 flex items-center justify-center">
        {index + 1}
      </div>
      <div className="flex-grow px-4 py-3">
        {typeof spec?.items[itemIndex] === 'string' ? (
          <HighlightedText
            text={spec?.items[itemIndex] as string}
            keywords={keywords}
            language={originalLanguage || Language.EN}
            showKeywords={showKeywords}
            className="sequencing-item-text"
          />
        ) : isChineseOriginal && spec?.items[itemIndex]?.textZh ? (
          <HighlightedText
            text={spec?.items[itemIndex].textZh as string}
            keywords={keywords}
            language={Language.ZH}
            showKeywords={showKeywords}
            className="sequencing-item-text"
          />
        ) : isMalayOriginal && spec?.items[itemIndex]?.textMs ? (
          <HighlightedText
            text={spec?.items[itemIndex].textMs as string}
            keywords={keywords}
            language={Language.MS}
            showKeywords={showKeywords}
            className="sequencing-item-text"
          />
        ) : (
          <HighlightedText
            text={spec?.items[itemIndex]?.textEn as string || ''}
            keywords={keywords}
            language={Language.EN}
            showKeywords={showKeywords}
            className="sequencing-item-text"
          />
        )}
      </div>
      <div className="flex items-center px-3 text-blue-500">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M5 9l4-4 4 4M5 15l4 4 4-4"/>
        </svg>
      </div>
    </div>
  );
};

const Sequencing: React.FC<BaseRendererProps<'SEQUENCING'>> = ({
  promptEn,
  promptZh,
  promptMs,
  originalPrompt,
  originalLanguage,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  keywords,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  showKeywords,
  onMouseUp
}) => {
  const [sequence, setSequence] = useState<number[]>([]);

  // Determine which language to use based on the question's original language
  const isChineseOriginal = originalLanguage === Language.ZH || originalPrompt === promptZh;
  const isMalayOriginal = originalLanguage === Language.MS || (originalPrompt !== promptZh && originalPrompt !== promptEn);
  const isEnglishOriginal = originalLanguage === Language.EN || (!isChineseOriginal && !isMalayOriginal);

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Minimum distance required before activating
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Initialize with shuffled items
  useEffect(() => {
    if (spec?.items) {
      // Create an array of indices and shuffle them
      const indices = Array.from({ length: spec.items.length }, (_, i) => i);
      // Simple Fisher-Yates shuffle
      for (let i = indices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [indices[i], indices[j]] = [indices[j], indices[i]];
      }
      setSequence(indices);
    }
  }, [spec]);

  // Update the parent component with the sequence
  useEffect(() => {
    if (sequence.length > 0) {
      onAnswerChange(JSON.stringify(sequence));
    }
  }, [sequence, onAnswerChange]);

  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSequence((items) => {
        const oldIndex = items.findIndex(id => id === active.id);
        const newIndex = items.findIndex(id => id === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt ? (
          <HighlightedText
            text={originalPrompt}
            keywords={keywords}
            language={originalLanguage || Language.EN}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : displayLanguage === 'en' ? (
          <HighlightedText
            text={promptEn}
            keywords={keywords}
            language={Language.EN}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : displayLanguage === 'ms' && promptMs ? (
          <HighlightedText
            text={promptMs}
            keywords={keywords}
            language={Language.MS}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : (
          <HighlightedText
            text={promptZh}
            keywords={keywords}
            language={Language.ZH}
            showKeywords={showKeywords}
            className="question-text"
          />
        )}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="space-y-3">
        <p className="text-sm text-gray-600 italic mb-2">
          Drag items to reorder them into the correct sequence
        </p>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={sequence}
            strategy={verticalListSortingStrategy}
          >
            {sequence.map((itemIndex, currentIndex) => (
              <SortableItem
                key={itemIndex}
                id={itemIndex}
                index={currentIndex}
                itemIndex={itemIndex}
                spec={spec}
                keywords={keywords}
                originalLanguage={originalLanguage}
                showKeywords={showKeywords}
                isChineseOriginal={isChineseOriginal}
                isMalayOriginal={isMalayOriginal}
              />
            ))}
          </SortableContext>
        </DndContext>
      </div>
    </RendererWrapper>
  );
};

export default Sequencing;
