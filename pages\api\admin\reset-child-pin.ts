import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { hashPin } from '../../../lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check authentication and admin role
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (session.user?.role !== 'ADMIN') {
    return res.status(403).json({ message: 'Forbidden - Admin access required' });
  }

  try {
    const { childId, newPin } = req.body;

    // Validate request body
    if (!childId || !newPin) {
      return res.status(400).json({ message: 'Child ID and new PIN are required' });
    }

    // Validate PIN format (6 digits)
    if (!/^\d{6}$/.test(newPin)) {
      return res.status(400).json({ message: 'PIN must be 6 digits' });
    }

    // Find the child to get the parent's salt
    const child = await prisma.child.findUnique({
      where: { id: Number(childId) },
      select: {
        id: true,
        salt: true,
        account: {
          select: {
            salt: true
          }
        }
      }
    });

    if (!child) {
      return res.status(404).json({ message: 'Child not found' });
    }

    // Use the child's salt or the parent's salt
    const salt = child.salt || child.account.salt;
    
    if (!salt) {
      return res.status(500).json({ message: 'Salt not found for hashing' });
    }

    // Hash the new PIN
    const { hash: pinHash } = await hashPin(newPin, salt);

    // Update the child's PIN
    await prisma.child.update({
      where: { id: Number(childId) },
      data: {
        pin_hash: pinHash
      }
    });

    res.status(200).json({ message: 'PIN reset successfully' });
  } catch (error) {
    console.error('Error resetting child PIN:', error);
    res.status(500).json({ message: 'Failed to reset PIN. Please try again.' });
  }
}
