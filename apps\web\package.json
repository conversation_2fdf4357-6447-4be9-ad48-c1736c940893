{"name": "web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@modelcontextprotocol/server-postgres": "^0.6.2", "@quiz/db": "*", "@tailwindcss/line-clamp": "^0.4.4", "@types/uuid": "^10.0.0", "argon2": "^0.43.0", "dayjs": "^1.11.13", "express": "^5.1.0", "formidable": "^3.5.4", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "next": "^15.3.1", "next-auth": "^4.24.7", "node-fetch": "^3.3.2", "pinyin": "^4.0.0-alpha.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.21.0", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "segmentit": "^2.0.3", "swr": "^2.3.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.1", "@types/formidable": "^3.4.5", "@types/lodash": "^4.17.16", "@types/node": "22.14.1", "@types/react": "19.1.2", "autoprefixer": "^10.4.0", "eslint": "9.26.0", "eslint-config-next": "15.3.2", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}}