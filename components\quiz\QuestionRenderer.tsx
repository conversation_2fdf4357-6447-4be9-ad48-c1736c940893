import React from 'react';
import { useQuiz } from './QuizContext';
import {
  MultipleChoice,
  MultipleChoiceImage,
  ShortAnswer,
  FillInTheBlank,
  TrueFalse,
  Matching,
  Sequencing,
  LongAnswer,
  PicturePrompt
} from '../renderers';
import TeachMeModal from './TeachMeModal';
import FlagButton from './FlagButton';

interface QuestionRendererProps {
  // No props needed as we'll use the context
}

const QuestionRenderer: React.FC<QuestionRendererProps> = () => {
  const {
    questions,
    currentIndex,
    selectedAnswer,
    setSelectedAnswer,
    displayLanguage,
    isAnswerIncorrect,
    isAnswerCorrect,
    submittedAnswer,
    getOriginalPrompt,
    handleNext,
    handleMouseUp,
    showKeywords,
    showHint,
    showTeachMe,
    setShowTeachMe,
    handleShowHint,
    handleFindEasierQuestion,
    noKeywordsMessage,
    childQuizLanguage // Add childQuizLanguage to the destructured values
  } = useQuiz();

  const currentQuestion = questions[currentIndex];
  const total = questions.length;

  // Check if currentQuestion is defined
  if (!currentQuestion) {
    console.error('Current question is undefined');
    return (
      <div className="text-white text-center p-4">
        <p className="text-xl mb-4">Error: Question data is missing</p>
        <p>Please try refreshing the page or return to the dashboard.</p>
      </div>
    );
  }

  // Common props for all renderers
  // Create a default empty spec object to avoid undefined errors
  const defaultSpec = {};

  // Determine which language to use for the question based on child's quiz language preference
  const useQuizLanguage = childQuizLanguage || currentQuestion.originalLanguage || 'ZH';

  // Debug output
  console.log('QuestionRenderer - childQuizLanguage:', childQuizLanguage);
  console.log('QuestionRenderer - originalLanguage:', currentQuestion.originalLanguage);
  console.log('QuestionRenderer - useQuizLanguage:', useQuizLanguage);

  // Get the appropriate prompt based on the quiz language
  const getPromptForQuizLanguage = () => {
    if (useQuizLanguage === 'EN') {
      return currentQuestion.promptEn || '';
    } else if (useQuizLanguage === 'ZH') {
      return currentQuestion.promptZh || '';
    } else if (useQuizLanguage === 'MS') {
      return currentQuestion.promptMs || currentQuestion.promptEn || '';
    }
    return getOriginalPrompt(currentQuestion) || '';
  };

  const commonProps = {
    questionId: currentQuestion.id,
    promptEn: currentQuestion.promptEn || '',
    promptZh: currentQuestion.promptZh || '',
    promptMs: currentQuestion.promptMs || '',
    // Use the prompt based on the child's quiz language preference
    originalPrompt: getPromptForQuizLanguage(),
    originalLanguage: useQuizLanguage, // Override with child's quiz language preference
    promptMediaUrl: currentQuestion.promptMedia?.url,
    promptMediaAlt: displayLanguage === 'en'
      ? currentQuestion.promptMedia?.altEn
      : displayLanguage === 'ms'
        ? currentQuestion.promptMedia?.altMs || currentQuestion.promptMedia?.altEn
        : currentQuestion.promptMedia?.altZh,
    keywords: currentQuestion.keywords,
    onAnswerChange: setSelectedAnswer,
    selectedAnswer,
    displayLanguage,
    showKeywords,
    onMouseUp: handleMouseUp,
    spec: currentQuestion.spec || defaultSpec,
    isIncorrect: isAnswerIncorrect,
    isCorrect: isAnswerCorrect,
    submittedAnswer: submittedAnswer,
    // Add a prop to disable answer selection when a correct answer has been submitted
    disableAnswerSelection: isAnswerCorrect && selectedAnswer === submittedAnswer
  };

  // Check if the question type is valid
  if (!currentQuestion.type) {
    console.error('Question type is missing');
    return (
      <div className="text-white text-center p-4">
        <p className="text-xl mb-4">Error: Question type is missing</p>
        <p className="mb-4">Please try refreshing the page or return to the dashboard.</p>
      </div>
    );
  }

  // Render based on question type (using string comparison to avoid enum issues)
  const questionType = currentQuestion.type.toString() || '';
  let renderedQuestion;

  if (questionType === 'MULTIPLE_CHOICE') {
    renderedQuestion = (
      <MultipleChoice
        {...commonProps}
        choices={currentQuestion.choices || []}
      />
    );
  } else if (questionType === 'MULTIPLE_CHOICE_IMAGE') {
    renderedQuestion = (
      <MultipleChoiceImage
        {...commonProps}
        spec={currentQuestion.spec}
        choices={currentQuestion.choices?.map(choice => ({
          ...choice,
          mediaUrl: choice.mediaUrl
        })) || []}
      />
    );
  } else if (questionType === 'SHORT_ANSWER') {
    renderedQuestion = (
      <ShortAnswer
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'FILL_IN_THE_BLANK') {
    renderedQuestion = (
      <FillInTheBlank
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'TRUE_FALSE') {
    renderedQuestion = (
      <TrueFalse
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'MATCHING') {
    renderedQuestion = (
      <Matching
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'SEQUENCING') {
    renderedQuestion = (
      <Sequencing
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'LONG_ANSWER') {
    renderedQuestion = (
      <LongAnswer
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else if (questionType === 'PICTURE_PROMPT') {
    renderedQuestion = (
      <PicturePrompt
        {...commonProps}
        spec={currentQuestion.spec}
      />
    );
  } else {
    // Fallback for unknown question types
    renderedQuestion = (
      <div className="text-white text-center p-4">
        <p className="text-xl mb-4">Unknown question type: {questionType}</p>
        <p className="mb-4">Please try refreshing the page or return to the dashboard.</p>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-2xl shadow-lg p-6 max-w-2xl mx-auto mt-6 mb-6 text-black">
        {renderedQuestion}
      </div>

      {/* Hint button - show after first wrong attempt */}
      {showHint && !showTeachMe && isAnswerIncorrect && (
        <div className="mt-4">
          <button
            onClick={handleShowHint}
            className="text-sm underline text-blue-600 hover:text-blue-800"
          >
            {displayLanguage === 'en' ? 'Need a hint?' :
             displayLanguage === 'ms' ? 'Perlukan petunjuk?' :
             '需要提示？'}
          </button>
        </div>
      )}

      {/* No keywords message */}
      {noKeywordsMessage && (
        <div className="mt-4 p-3 bg-yellow-100 text-yellow-800 rounded-md">
          {displayLanguage === 'en'
            ? 'No keywords available for this question.'
            : displayLanguage === 'ms'
              ? 'Tiada kata kunci tersedia untuk soalan ini.'
              : '此问题没有可用的关键词。'}
        </div>
      )}

      {/* Answer feedback and navigation */}
      <div className="mt-6">
        {/* Show correct feedback when answer is correct */}
        {isAnswerCorrect && (
          <div className="bg-green-500 text-white p-4 rounded-t-lg flex items-center mb-4 relative">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-bold text-xl">
              {displayLanguage === 'en' ? 'Nice!' : displayLanguage === 'ms' ? 'Bagus!' : '很好！'}
            </span>

            {/* Flag button for correct answers */}
            <div className="absolute top-0 right-0">
              <FlagButton questionId={currentQuestion.id} selectedAnswer={selectedAnswer} />
            </div>
          </div>
        )}

        {/* Show incorrect feedback with flag button */}
        {isAnswerIncorrect && (
          <div className="bg-red-500 text-white p-4 rounded-t-lg flex items-center mb-4 relative">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="font-bold text-xl">
              {displayLanguage === 'en' ? 'Try Again' : displayLanguage === 'ms' ? 'Cuba Lagi' : '再试一次'}
            </span>

            {/* Flag button for incorrect answers */}
            <div className="absolute top-0 right-0">
              <FlagButton questionId={currentQuestion.id} selectedAnswer={selectedAnswer} />
            </div>
          </div>
        )}

        {/* Show navigation button when there's a selected answer or it's a short answer question */}
        {(selectedAnswer || (currentQuestion && currentQuestion.type === 'SHORT_ANSWER')) && (
          <button
            onClick={handleNext}
            className={`w-full py-2 px-4 text-white font-semibold rounded focus:outline-none ${
              isAnswerCorrect
                ? 'bg-green-500 hover:bg-green-600'
                : isAnswerIncorrect
                  ? 'bg-blue-500 hover:bg-blue-600'
                  : 'bg-orange-500 hover:bg-orange-600'
            }`}
          >
            {isAnswerCorrect
              ? (currentIndex === total - 1
                  ? (displayLanguage === 'en' ? 'Finish Quiz' : displayLanguage === 'ms' ? 'Selesai' : '完成')
                  : (displayLanguage === 'en' ? 'Continue' : displayLanguage === 'ms' ? 'Teruskan' : '继续'))
              : isAnswerIncorrect
                ? (displayLanguage === 'en' ? 'Try Again' : '再试一次')
                : (currentIndex === total - 1
                    ? (displayLanguage === 'en' ? 'Finish Quiz' : '完成测验')
                    : (displayLanguage === 'en' ? 'Next' : '下一题'))
            }
          </button>
        )}
      </div>

      {/* Teach Me modal - show after three wrong attempts */}
      {showTeachMe && currentQuestion.explanation && (
        <TeachMeModal
          explanation={
            // Get explanation text safely with type assertion
            (currentQuestion.explanation as any).en ||
            (currentQuestion.explanation as any).textEn ||
            ''
          }
          onClose={() => setShowTeachMe(false)}
          onFindEasier={handleFindEasierQuestion}
          displayLanguage={displayLanguage}
        />
      )}
    </>
  );
};

export default QuestionRenderer;
