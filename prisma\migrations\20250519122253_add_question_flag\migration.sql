-- CreateEnum
CREATE TYPE "FlagStatus" AS ENUM ('PENDING', 'REVIEWED', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "TG_QuestionFlag" (
    "id" SERIAL NOT NULL,
    "questionId" INTEGER NOT NULL,
    "childId" INTEGER NOT NULL,
    "quizAttemptId" INTEGER NOT NULL,
    "submittedKey" TEXT,
    "submittedText" TEXT,
    "submittedJson" JSONB,
    "status" "FlagStatus" NOT NULL DEFAULT 'PENDING',
    "reviewNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reviewedAt" TIMESTAMP(3),

    CONSTRAINT "TG_QuestionFlag_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "TG_QuestionFlag" ADD CONSTRAINT "TG_QuestionFlag_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_QuestionFlag" ADD CONSTRAINT "TG_QuestionFlag_childId_fkey" FOREIGN KEY ("childId") REFERENCES "TG_Child"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_QuestionFlag" ADD CONSTRAINT "TG_QuestionFlag_quizAttemptId_fkey" FOREIGN KEY ("quizAttemptId") REFERENCES "TG_QuizAttempt"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
