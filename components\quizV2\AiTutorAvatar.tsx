import React from 'react';

interface AiTutorAvatarProps {
  size?: 'sm' | 'md' | 'lg';
}

export default function AiTutorAvatar({ size = 'md' }: AiTutorAvatarProps) {
  // Size classes
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-full h-full'
  };

  return (
    <div className={`${sizeClasses[size]} flex items-center justify-center text-white overflow-hidden`}>
      {/* Hexagonal pattern SVG */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="#0A8CBF"
        className="w-full h-full"
      >
        <path d="M12 2L4 6v12l8 4 8-4V6L12 2zm0 2.6L18 8v8l-6 3-6-3V8l6-3.4z" />
        <path d="M12 6l-4 2v4l4 2 4-2V8l-4-2zm0 1.3l2 1v2l-2 1-2-1v-2l2-1z" />
        <path d="M6 10l-1 .5v3l3 1.5 1-.5v-3l-3-1.5zm12 0l-3 1.5v3l1 .5 3-1.5v-3l-1-.5z" />
        <path d="M12 16l-1 .5v1l1 .5 1-.5v-1l-1-.5z" />
      </svg>
    </div>
  );
}
