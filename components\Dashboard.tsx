import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/router';
import HomeworkPanel from './HomeworkPanel';

interface IncompleteQuiz {
  id: number;
  currentQuestionIndex: number;
  quizType: string;
  startTime: string;
  subject: {
    name: string;
  };
  unit?: {
    unitNumber: number;
  };
}

export default function Dashboard() {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [incompleteQuiz, setIncompleteQuiz] = useState<IncompleteQuiz | null>(null);
  const [totalQuestions, setTotalQuestions] = useState<number | null>(null);

  const { data: session } = useSession();
  const user = session?.user;
  const router = useRouter();

  // Fetch incomplete quiz on component mount
  useEffect(() => {
    if (user?.id) {
      fetch(`/api/quiz/incomplete?childId=${user.id}`)
        .then(res => res.json())
        .then(data => {
          if (data.incompleteQuiz) {
            setIncompleteQuiz(data.incompleteQuiz);
          }
        })
        .catch(error => console.error('Error fetching incomplete quiz:', error));
    }
  }, [user?.id]);

  // Fetch total questions when incompleteQuiz is set
  useEffect(() => {
    if (incompleteQuiz?.id) {
      fetch(`/api/quiz/total-questions?attemptId=${incompleteQuiz.id}`)
        .then(res => res.json())
        .then(data => {
          if (data.totalQuestions !== undefined) {
            setTotalQuestions(data.totalQuestions);
          }
        })
        .catch(error => console.error('Error fetching total questions:', error));
    } else {
      setTotalQuestions(null); // Reset total questions if no incomplete quiz
    }
  }, [incompleteQuiz?.id]);


  // Mock student data (will be replaced by actual user data)
  const studentData = {
    name: user?.name || 'Student',
    grade: user?.role === 'CHILD' ? 'Year 5' : '', // Assuming 'CHILD' role implies Year 5 for now
    pendingHomework: 2 // Keep mock data for now
  };

  const handleContinueQuiz = () => {
    if (incompleteQuiz) {
      router.push(`/quiz/${incompleteQuiz.id}`);
    }
  };


  // Removed mockTopUsers as we're simplifying the dashboard

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-blue-600">StudentQuiz</div>
            <span className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full">Student Portal</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right mr-4">
              <div className="font-medium text-gray-900">{studentData.name}</div>
              <div className="text-sm text-gray-500">{studentData.grade}</div>
            </div>
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center space-x-2 focus:outline-none"
              >
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white">
                  {studentData.name.charAt(0)}
                </div>
                <span>▼</span>
              </button>
              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
                  <div className="px-4 py-2 text-xs text-gray-500">Signed in as {user?.role}</div>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Progress</a>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Study Schedule</a>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                  <div className="border-t border-gray-100"></div>
                  <button
                    onClick={() => signOut({ callbackUrl: '/login' })}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 focus:outline-none"
                  >
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Student Welcome Banner */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-2xl font-semibold">Welcome back, {studentData.name}!</h1>
          <p className="mt-1 text-blue-100">
            {incompleteQuiz
              ? `You have an incomplete quiz in ${incompleteQuiz.subject.name} ${incompleteQuiz.unit ? `Unit ${incompleteQuiz.unit.unitNumber}` : ''}. Click "Continue Quiz" to resume.`
              : 'Welcome to your student dashboard!'}
          </p>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Homework Section - Now First */}
          <div className="bg-white rounded-lg shadow p-6">
            {user?.id ? (
              <HomeworkPanel
                childId={user.id}
                lang={user.preferredLanguage || 'ZH'}
              />
            ) : (
              <div className="text-gray-500 text-center py-8">
                Please sign in to view your homework
              </div>
            )}

          </div>

          {/* Subject and Topic Selection */}
          <div className="bg-white rounded-lg shadow p-6">
                <div className="text-center py-8">
                  <h2 className="text-xl font-semibold mb-4">Get Help</h2>
                  <p className="text-gray-500">No tutors are available at the moment. Please check back later.</p>
                </div>
          </div>
        </div>

        {/* Learning Tools Section */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Learning Tools</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* Flashcards Tile */}
            <div
              onClick={() => router.push('/flashcards')}
              className="bg-white hover:bg-blue-100 cursor-pointer p-6 rounded-lg shadow flex flex-col items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-blue-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h18M3 12h18M3 19h18" />
              </svg>
              <span className="font-semibold">Flashcards</span>
            </div>

            {/* Additional tools can be added here in the future */}
          </div>
        </div>

        {/* Additional sections can be added here in the future */}
      </main>
    </div>
  );
}
