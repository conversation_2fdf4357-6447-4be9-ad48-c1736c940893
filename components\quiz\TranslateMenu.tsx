import React from 'react';

interface TranslateMenuProps {
  menuProps: {
    style: {
      position: 'fixed';
      top: number;
      left: number;
      zIndex: number;
    };
    className: string;
    onClick: (e: React.MouseEvent) => void;
  } | null;
  translatedText: string | null;
  onTranslate: () => Promise<void>;
  displayLanguage: 'en' | 'zh';
}

/**
 * Component that renders the translation menu when text is selected
 */
const TranslateMenu: React.FC<TranslateMenuProps> = ({
  menuProps,
  translatedText,
  onTranslate,
  displayLanguage
}) => {
  if (!menuProps) return null;

  return (
    <div
      style={menuProps.style}
      className={menuProps.className}
      onClick={menuProps.onClick}
    >
      {translatedText ? (
        <div className="px-4 py-2">{translatedText}</div>
      ) : (
        <button
          onClick={onTranslate}
          className="text-blue-500 hover:underline px-4 py-2"
        >
          {displayLanguage === 'en' ? 'Translate' : '翻译'}
        </button>
      )}
    </div>
  );
};

export default TranslateMenu;
