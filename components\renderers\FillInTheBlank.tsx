import React, { useState, useEffect } from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';

const FillInTheBlank: React.FC<BaseRendererProps<'FILL_IN_THE_BLANK'>> = ({
  promptEn,
  promptZh,
  originalPrompt,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp
}) => {
  const [answers, setAnswers] = useState<string[]>([]);

  // Initialize answers array based on the number of blanks
  useEffect(() => {
    if (spec && spec.blanks) {
      setAnswers(Array(spec.blanks.length).fill(''));
    }
  }, [spec]);

  // Update the parent component with the combined answer string
  useEffect(() => {
    if (answers.length > 0) {
      onAnswerChange(JSON.stringify(answers));
    }
  }, [answers, onAnswerChange]);

  // Parse the template and replace placeholders with input fields
  const renderTemplate = () => {
    if (!spec || !spec.template) return null;

    const template = spec.template;
    const parts = template.split('___');

    return (
      <div className="fill-blank-template">
        {parts.map((part, index) => (
          <React.Fragment key={index}>
            <span>{part}</span>
            {index < parts.length - 1 && (
              <input
                type="text"
                value={answers[index] || ''}
                onChange={(e) => {
                  const newAnswers = [...answers];
                  newAnswers[index] = e.target.value;
                  setAnswers(newAnswers);
                }}
                className="mx-1 px-2 py-1 border-b-2 border-blue-500 bg-transparent text-white w-24 focus:outline-none"
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="mb-4">
        {renderTemplate()}
      </div>
    </RendererWrapper>
  );
};

export default FillInTheBlank;
