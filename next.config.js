/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  webpack: (config) => {
    return config;
  },
  images: {
    unoptimized: true,
  },
  // Configure static file serving for development
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      },
    ];
  },
  // Explicitly expose environment variables
  env: {
    NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2,
  },
};

module.exports = nextConfig;
