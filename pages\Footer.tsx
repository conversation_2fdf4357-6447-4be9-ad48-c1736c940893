import React from 'react';
import { HomeIcon, BookIcon, TrophyIcon, UserIcon } from 'lucide-react';
interface FooterProps {
  currentScreen: string;
}
export const Footer: React.FC<FooterProps> = ({
  currentScreen
}) => {
  return <footer className="sticky bottom-0 w-full bg-white border-t border-gray-200 py-2">
      <div className="flex justify-around items-center">
        <button className={`flex flex-col items-center p-2 ${currentScreen === 'home' ? 'text-[#0F5FA6]' : 'text-gray-500'}`}>
          <HomeIcon size={24} />
          <span className="text-xs mt-1">Home</span>
        </button>
        <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
          <BookIcon size={24} />
          <span className="text-xs mt-1">Lessons</span>
        </button>
        <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
          <TrophyIcon size={24} />
          <span className="text-xs mt-1">Achievements</span>
        </button>
        <button className="flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]">
          <UserIcon size={24} />
          <span className="text-xs mt-1">Profile</span>
        </button>
      </div>
    </footer>;
};