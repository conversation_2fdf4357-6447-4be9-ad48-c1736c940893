import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Check if the user is an admin
  if (session.user?.role !== 'ADMIN') {
    return res.status(403).json({ message: 'Forbidden - Admin access required' });
  }

  try {
    // Fetch all flagged questions with related data
    const flaggedQuestions = await prisma.questionFlag.findMany({
      include: {
        question: {
          select: {
            id: true,
            questionId: true,
            type: true,
            promptEn: true,
            promptZh: true,
            subject: {
              select: {
                name: true,
              },
            },
            unit: {
              select: {
                unitNumber: true,
                topicEn: true,
              },
            },
          },
        },
        child: {
          select: {
            name: true,
            username: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return res.status(200).json(flaggedQuestions);
  } catch (error) {
    console.error('Error fetching flagged questions:', error);
    return res.status(500).json({ message: 'An error occurred while fetching flagged questions' });
  }
}
