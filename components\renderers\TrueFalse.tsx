import React from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';
import HighlightedText from './HighlightedText';
import { Language } from '@prisma/client';
import { useQuiz } from '../quiz/QuizContext';

const TrueFalse: React.FC<BaseRendererProps<'TRUE_FALSE'>> = ({
  promptEn,
  promptZh,
  promptMs,
  originalPrompt,
  originalLanguage,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  keywords,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  showKeywords,
  onMouseUp,
  isIncorrect,
  isCorrect,
  submittedAnswer,
  disableAnswerSelection
}) => {
  // Get the handleNext function from the QuizContext
  const { handleNext } = useQuiz();

  // Determine which language to use based on the question's original language
  const isChineseOriginal = originalLanguage === 'ZH' || originalPrompt === promptZh;
  const isMalayOriginal = originalLanguage === 'MS' || (originalPrompt === promptMs && promptMs);

  const handleSelect = (value: string) => {
    // Don't allow changing the answer if it's already correct and submitted
    if (disableAnswerSelection) {
      return;
    }
    onAnswerChange(value);
  };

  // Statement is handled directly in the JSX below

  return (
    <RendererWrapper onMouseUp={onMouseUp} data-testid="true-false-renderer">
      <div className="text-lg font-medium mb-6 text-black">
        {/* Display the main question prompt with keyword highlighting */}
        {originalPrompt ? (
          <HighlightedText
            text={originalPrompt}
            keywords={keywords}
            language={originalLanguage || Language.EN}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : displayLanguage === 'en' ? (
          <HighlightedText
            text={promptEn}
            keywords={keywords}
            language={Language.EN}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : displayLanguage === 'ms' && promptMs ? (
          <HighlightedText
            text={promptMs}
            keywords={keywords}
            language={Language.MS}
            showKeywords={showKeywords}
            className="question-text"
          />
        ) : (
          <HighlightedText
            text={promptZh}
            keywords={keywords}
            language={Language.ZH}
            showKeywords={showKeywords}
            className="question-text"
          />
        )}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      {/* Always show the True/False options */}
      <div className="mb-4">
        {/* Only show the statement separately if it's different from the prompt and comes from spec */}
        {spec?.statement && spec.statement !== originalPrompt && (
          <div className="text-lg font-medium mb-4">
            <HighlightedText
              text={spec.statement}
              keywords={keywords}
              language={originalLanguage || Language.EN}
              showKeywords={showKeywords}
              className="question-text"
            />
          </div>
        )}

        <div className="space-y-3">
          <button
            onClick={() => handleSelect('TRUE')}
            disabled={disableAnswerSelection}
            className={`w-full text-left rounded px-4 py-3 flex items-center justify-between ${
              selectedAnswer === 'TRUE' || selectedAnswer === 'true'
                ? isIncorrect && selectedAnswer === submittedAnswer
                  ? 'bg-red-300' // Red background for incorrect answers
                  : 'bg-blue-300' // Blue background for selected but not submitted or not incorrect
                : 'bg-white text-black' // Default background
            } ${disableAnswerSelection && !(selectedAnswer === 'TRUE' || selectedAnswer === 'true') ? 'opacity-60 cursor-not-allowed' : ''}`}
          >
            <span>{isChineseOriginal ? '正确' : isMalayOriginal ? 'Benar' : 'True'}</span>
            {selectedAnswer === 'TRUE' && isIncorrect && selectedAnswer === submittedAnswer && (
              <div className="text-red-700 font-bold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                {displayLanguage === 'en' ? 'Incorrect' : displayLanguage === 'ms' ? 'Tidak betul' : '不正确'}
              </div>
            )}
            {selectedAnswer === 'TRUE' && isCorrect && selectedAnswer === submittedAnswer && (
              <div className="text-green-700 font-bold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {displayLanguage === 'en' ? 'Correct!' : displayLanguage === 'ms' ? 'Betul!' : '正确！'}
              </div>
            )}
          </button>

          <button
            onClick={() => handleSelect('FALSE')}
            disabled={disableAnswerSelection}
            className={`w-full text-left rounded px-4 py-3 flex items-center justify-between ${
              selectedAnswer === 'FALSE' || selectedAnswer === 'false'
                ? isIncorrect && selectedAnswer === submittedAnswer
                  ? 'bg-red-300' // Red background for incorrect answers
                  : 'bg-blue-300' // Blue background for selected but not submitted or not incorrect
                : 'bg-white text-black' // Default background
            } ${disableAnswerSelection && !(selectedAnswer === 'FALSE' || selectedAnswer === 'false') ? 'opacity-60 cursor-not-allowed' : ''}`}
          >
            <span>{isChineseOriginal ? '错误' : isMalayOriginal ? 'Salah' : 'False'}</span>
            {selectedAnswer === 'FALSE' && isIncorrect && selectedAnswer === submittedAnswer && (
              <div className="text-red-700 font-bold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                {displayLanguage === 'en' ? 'Incorrect' : displayLanguage === 'ms' ? 'Tidak betul' : '不正确'}
              </div>
            )}
            {selectedAnswer === 'FALSE' && isCorrect && selectedAnswer === submittedAnswer && (
              <div className="text-green-700 font-bold flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {displayLanguage === 'en' ? 'Correct!' : displayLanguage === 'ms' ? 'Betul!' : '正确！'}
              </div>
            )}
          </button>
        </div>
      </div>
    </RendererWrapper>
  );
};

export default TrueFalse;


