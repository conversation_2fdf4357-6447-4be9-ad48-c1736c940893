import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { QuizStatus } from '@prisma/client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Use getServerSession for server-side session retrieval
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { quizAttemptId, score, totalQuestions, childId } = req.body;

  if (!quizAttemptId) {
    return res.status(400).json({ message: 'Quiz attempt ID is required' });
  }

  console.log('Marking quiz as completed:', { quizAttemptId, score, totalQuestions, childId });

  try {
    // Find the quiz attempt
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: {
        id: Number(quizAttemptId),
      },
      select: {
        id: true,
        childId: true,
        status: true,
      },
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    // Verify that the logged-in user is the child who owns the quiz attempt
    if (session.user?.id !== quizAttempt.childId.toString()) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    // Check if the quiz is already completed
    if (quizAttempt.status === QuizStatus.COMPLETED) {
      console.log('Quiz is already marked as completed:', quizAttempt.id);
      return res.status(200).json({
        message: 'Quiz is already marked as completed',
        quizAttempt
      });
    }

    console.log('Quiz found, current status:', quizAttempt.status);

    // Update the quiz attempt to mark it as completed
    console.log('Updating quiz attempt to mark as completed:', {
      id: Number(quizAttemptId),
      status: QuizStatus.COMPLETED,
      score: score !== undefined ? score : null
    });

    const updatedQuizAttempt = await prisma.quizAttempt.update({
      where: {
        id: Number(quizAttemptId),
      },
      data: {
        status: QuizStatus.COMPLETED,
        endTime: new Date(),
        score: score !== undefined ? score : null,
      },
    });

    console.log('Quiz attempt updated successfully:', {
      id: updatedQuizAttempt.id,
      status: updatedQuizAttempt.status,
      endTime: updatedQuizAttempt.endTime,
      score: updatedQuizAttempt.score
    });

    res.status(200).json({
      message: 'Quiz attempt marked as completed successfully',
      quizAttempt: updatedQuizAttempt
    });
  } catch (error) {
    console.error('Error marking quiz as completed:', error);
    res.status(500).json({ message: 'Error marking quiz as completed' });
  }
}
