import { useState } from 'react';
import cards from '../data/flashcards.json';
import Flashcard from '../components/Flashcard';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

export default function FlashcardApp() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === 'loading') return null;
  if (!session || session.user.role !== 'CHILD') {
    router.push('/student-dashboard');
    return null;
  }

  const [index, setIndex] = useState(0);
  const next = () => setIndex(i => (i + 1) % cards.length);

  return (
    <div className="min-h-screen bg-blue-600 flex flex-col items-center justify-center space-y-6 p-4 text-white">
      <h1 className="text-3xl font-bold">Flashcards</h1>
      <Flashcard card={cards[index]} />
      <button
        onClick={next}
        className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded"
      >
        Next
      </button>
      <button
        onClick={() => router.push('/student-dashboard')}
        className="text-sm underline mt-4"
      >
        Back to Dashboard
      </button>

      {/* Debug section */}
      <div className="mt-8 p-4 bg-white text-black rounded-lg text-sm">
        <h3 className="font-bold mb-2">Debug Info:</h3>
        <p>Current card data:</p>
        <pre className="bg-gray-100 p-2 rounded mt-1 overflow-auto">
          {JSON.stringify(cards[index], null, 2)}
        </pre>
      </div>
    </div>
  );
}
