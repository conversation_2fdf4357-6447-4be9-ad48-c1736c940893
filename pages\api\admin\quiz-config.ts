import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';
import { QuizMode } from '@prisma/client';

// Type for the request body
interface UpdateQuizConfigRequest {
  mode: QuizMode;
  numQuestions?: number;
  questionTypes?: string[];
  allowTranslate?: boolean;
  allowHints?: boolean;
  allowAiTutor?: boolean;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication and admin role
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (session.user?.role !== 'ADMIN') {
    return res.status(403).json({ message: 'Forbidden - Admin access required' });
  }

  // Handle GET request
  if (req.method === 'GET') {
    try {
      const { mode } = req.query;

      // If mode is provided, return the specific config
      if (mode && typeof mode === 'string') {
        const quizConfig = await prisma.quizConfig.findUnique({
          where: { mode: mode as QuizMode },
        });

        if (!quizConfig) {
          return res.status(404).json({ message: `Quiz config for mode ${mode} not found` });
        }

        return res.status(200).json(quizConfig);
      }

      // Otherwise, return all configs
      const quizConfigs = await prisma.quizConfig.findMany();
      return res.status(200).json(quizConfigs);
    } catch (error) {
      console.error('Error fetching quiz config:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Handle PUT request
  if (req.method === 'PUT') {
    try {
      const {
        mode,
        numQuestions,
        questionTypes,
        allowTranslate,
        allowHints,
        allowAiTutor,
      } = req.body as UpdateQuizConfigRequest;

      // Validate required fields
      if (!mode) {
        return res.status(400).json({ message: 'Quiz mode is required' });
      }

      // Check if the config exists
      const existingConfig = await prisma.quizConfig.findUnique({
        where: { mode },
      });

      if (!existingConfig) {
        return res.status(404).json({ message: `Quiz config for mode ${mode} not found` });
      }

      // Update the config
      const updatedConfig = await prisma.quizConfig.update({
        where: { mode },
        data: {
          numQuestions: numQuestions !== undefined ? numQuestions : undefined,
          questionTypes: questionTypes !== undefined ? questionTypes : undefined,
          allowTranslate: allowTranslate !== undefined ? allowTranslate : undefined,
          allowHints: allowHints !== undefined ? allowHints : undefined,
          allowAiTutor: allowAiTutor !== undefined ? allowAiTutor : undefined,
        },
      });

      return res.status(200).json(updatedConfig);
    } catch (error) {
      console.error('Error updating quiz config:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Handle unsupported methods
  return res.status(405).json({ message: 'Method not allowed' });
}
