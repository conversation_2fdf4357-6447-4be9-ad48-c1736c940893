import React from 'react';
import ReactMarkdown from 'react-markdown';

export interface ChatMessage {
  id: string;
  sender: 'ai' | 'student';
  content: string;
  timestamp: Date;
}

interface ChatBubbleProps {
  message: ChatMessage;
  displayLanguage: 'en' | 'zh';
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message, displayLanguage }) => {
  const isAi = message.sender === 'ai';
  
  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`flex ${isAi ? 'justify-start' : 'justify-end'} mb-3`}>
      <div 
        className={`max-w-[80%] rounded-lg px-4 py-2 ${
          isAi 
            ? 'bg-white text-gray-800 rounded-tl-none' 
            : 'bg-green-500 text-white rounded-tr-none'
        }`}
      >
        {isAi && (
          <div className="font-bold mb-1">
            {displayLanguage === 'en' ? 'AI Tutor' : 'AI 导师'}
          </div>
        )}
        <div className="prose prose-sm max-w-none">
          <ReactMarkdown>{message.content}</ReactMarkdown>
        </div>
        <div className={`text-xs mt-1 text-right ${isAi ? 'text-gray-500' : 'text-green-200'}`}>
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

export default ChatBubble;
