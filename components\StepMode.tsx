import React from 'react';
import { QuizMode } from './StartQuiz';

interface StepModeProps {
  onSelect: (mode: QuizMode) => void;
}

const StepMode: React.FC<StepModeProps> = ({ onSelect }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Select Quiz Mode</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => {
            console.log('Mode selected: mastery');
            onSelect('mastery');
          }}
          className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-blue-50 hover:bg-blue-100"
        >
          <h3 className="text-lg font-medium mb-2">Mastery Quiz</h3>
          <p className="text-gray-600">Practice to master specific topics and track your progress over time.</p>
        </button>
        
        <button
          onClick={() => {
            console.log('Mode selected: test');
            onSelect('test');
          }}
          className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-purple-50 hover:bg-purple-100"
        >
          <h3 className="text-lg font-medium mb-2">Test Quiz</h3>
          <p className="text-gray-600">Challenge yourself with a timed test to simulate exam conditions.</p>
        </button>
        
        <button
          onClick={() => {
            console.log('Mode selected: quick');
            onSelect('quick');
          }}
          className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-green-50 hover:bg-green-100"
        >
          <h3 className="text-lg font-medium mb-2">Quick Quiz</h3>
          <p className="text-gray-600">Jump right into a random selection of questions across all subjects.</p>
        </button>
      </div>
    </div>
  );
};

export default StepMode;
