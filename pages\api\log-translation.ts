import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { childId, questionId, translatedText } = req.body;

  if (!childId || !questionId || !translatedText) {
    return res.status(400).json({ message: 'Missing required fields' });
  }

  try {
    const translationLog = await prisma.translationLog.create({
      data: {
        childId: parseInt(childId, 10),
        questionId: parseInt(questionId, 10),
        translatedText,
      },
    });
    res.status(201).json(translationLog);
  } catch (error) {
    console.error('Error logging translation:', error);
    res.status(500).json({ message: 'Error logging translation' });
  } finally {
    await prisma.$disconnect();
  }
}
