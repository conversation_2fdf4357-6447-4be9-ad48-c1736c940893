import React, { ReactNode, useState, useRef, useEffect } from 'react';
import { useQuizV2 } from './QuizV2Provider';
import useTranslateV2 from '../../hooks/useTranslateV2';
import { useTranslateBubble } from '../../hooks/useTranslateBubble';
import AiTutorPanel from './AiTutorPanel';
import AiTutorAvatar from './AiTutorAvatar';
import HintSheet from './HintSheet';
import AiTutorNotification from './AiTutorNotification';
import { getFromLocalStorage, setInLocalStorage } from '../../hooks/useLocalStorage';

interface LayoutProps {
  children: ReactNode;
}

export default function QuizV2Layout({ children }: LayoutProps) {
  const {
    questions,
    currentIndex,
    displayLanguage,
    allowTranslate,
    allowAiTutor,
    helpEnabled
  } = useQuizV2();

  // Initialize AI tutor visibility - check localStorage for previous state
  const [isAiTutorVisible, setIsAiTutorVisible] = useState(false);

  // Load AI tutor visibility state from localStorage on mount
  useEffect(() => {
    const savedState = getFromLocalStorage('ai_tutor_visible', false);
    setIsAiTutorVisible(savedState);
  }, []);

  // Save AI tutor visibility state to localStorage when it changes
  useEffect(() => {
    setInLocalStorage('ai_tutor_visible', isAiTutorVisible);
    console.log('💾 Saved AI tutor visibility state:', isAiTutorVisible);
  }, [isAiTutorVisible]);

  const [unreadMessages, setUnreadMessages] = useState(1); // Start with 1 unread message

  // State for AI tutor notification
  const [aiTutorNotification, setAiTutorNotification] = useState<string | null>(null);

  // Get the current question and onMouseUp handler from context
  const currentQuestion = questions[currentIndex];
  const { onMouseUp } = useQuizV2();

  // Reference to the addTranslation function from AiTutorPanel
  const addTranslationRef = useRef<((translatedText: string, originalText: string) => void) | null>(null);

  // Debug: Log when the reference changes
  useEffect(() => {
    console.log('🔄 addTranslationRef status:', addTranslationRef.current ? 'Available' : 'Not available');
  }, [addTranslationRef.current]);

  // Initialize the translation hooks
  const t = useTranslateV2(currentQuestion, displayLanguage, allowTranslate);
  const translateBubble = useTranslateBubble(allowTranslate && helpEnabled, currentQuestion);

  // Toggle AI tutor visibility
  const toggleAiTutor = () => {
    const newVisibility = !isAiTutorVisible;
    setIsAiTutorVisible(newVisibility);

    // Reset unread messages and clear notification when opening the AI tutor panel
    if (newVisibility) {
      setUnreadMessages(0);
      setAiTutorNotification(null); // Clear any AI tutor notification
    }
  };

  // Handle translation results and send them to the AI tutor panel
  useEffect(() => {
    if (t.translatedText && t.translatedText !== 'Translating...' && t.contextMenu && addTranslationRef.current) {
      console.log('🔍 Translation completed in Layout, sending to AI tutor panel:', {
        translatedText: t.translatedText,
        originalText: t.contextMenu.text
      });

      // Send the translation to the AI tutor panel
      addTranslationRef.current(t.translatedText, t.contextMenu.text);

      // Create translation message for notification
      const translationPrefix = displayLanguage === 'en'
        ? 'Translation of "'
        : displayLanguage === 'ms'
          ? 'Terjemahan untuk "'
          : '翻译 "';

      const translationSuffix = displayLanguage === 'en'
        ? '": '
        : displayLanguage === 'ms'
          ? '": '
          : '": ';

      const message = `${translationPrefix}${t.contextMenu.text}${translationSuffix}${t.translatedText}`;

      // If AI tutor is not visible, show notification instead of expanding the panel
      if (!isAiTutorVisible) {
        setAiTutorNotification(message);
        // Increment unread message count
        setUnreadMessages(prev => prev + 1);
      }
    }
  }, [t.translatedText, t.contextMenu, isAiTutorVisible, displayLanguage]);

  // Handle translation bubble results and send them to the AI tutor panel
  useEffect(() => {
    const handleTranslationComplete = (event: CustomEvent) => {
      console.log('🎯 translationComplete event received in Layout:', event.detail);

      if (event.detail && event.detail.translatedText && event.detail.originalText && addTranslationRef.current) {
        console.log('🔍 Translation event details valid, sending to AI tutor panel:', {
          translatedText: event.detail.translatedText,
          originalText: event.detail.originalText
        });

        // Send the translation to the AI tutor panel
        addTranslationRef.current(event.detail.translatedText, event.detail.originalText);

        // Create translation message for notification
        const translationPrefix = displayLanguage === 'en'
          ? 'Translation of "'
          : displayLanguage === 'ms'
            ? 'Terjemahan untuk "'
            : '翻译 "';

        const translationSuffix = displayLanguage === 'en'
          ? '": '
          : displayLanguage === 'ms'
            ? '": '
            : '": ';

        const message = `${translationPrefix}${event.detail.originalText}${translationSuffix}${event.detail.translatedText}`;

        // If AI tutor is not visible, show notification instead of expanding the panel
        if (!isAiTutorVisible) {
          setAiTutorNotification(message);
          // Increment unread message count
          setUnreadMessages(prev => prev + 1);
        }
      } else {
        console.warn('⚠️ Translation event received but missing required data or addTranslationRef not available:', {
          hasDetail: !!event.detail,
          hasTranslatedText: event.detail?.translatedText,
          hasOriginalText: event.detail?.originalText,
          hasAddTranslationRef: !!addTranslationRef.current
        });
      }
    };

    // Listen for the custom event
    window.addEventListener('translationComplete', handleTranslationComplete as EventListener);

    return () => {
      window.removeEventListener('translationComplete', handleTranslationComplete as EventListener);
    };
  }, [isAiTutorVisible, displayLanguage]);

  return (
    <div
      className="min-h-screen flex flex-col bg-[#f0f4f8] text-gray-800 relative"
      onClick={t.clearContextMenu}
    >
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="flex flex-col md:flex-row w-full max-w-6xl gap-4">
          {/* Main content */}
          <div
            className={`bg-white rounded-2xl shadow-lg ${isAiTutorVisible ? 'md:w-3/5' : 'w-full'} overflow-hidden flex flex-col`}
            onMouseUp={onMouseUp}
          >
            {children}
          </div>

          {/* AI Tutor panel - overlay on mobile, side-by-side on desktop */}
          {isAiTutorVisible && allowAiTutor && (
            <>
              {/* Mobile overlay version (hidden on md and above) */}
              <div className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-50" onClick={() => {
                setIsAiTutorVisible(false);
                setUnreadMessages(0);
              }}></div>
              <div className="md:hidden fixed top-[10%] left-4 right-4 bottom-[10%] z-50 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col max-w-lg mx-auto">
                <AiTutorPanel
                  onCollapse={() => {
                    setIsAiTutorVisible(false);
                    setUnreadMessages(0);
                  }}
                  onNewMessage={(count) => setUnreadMessages(prev => prev + count)}
                  onAddTranslation={(callback) => {
                    addTranslationRef.current = callback;
                  }}
                  onAiMessage={(message) => {
                    // Store the message for notification when panel is collapsed
                    if (!isAiTutorVisible) {
                      setAiTutorNotification(message);
                    }
                  }}
                />
              </div>

              {/* Desktop side-by-side version (hidden on small screens) */}
              <div className="hidden md:flex bg-white rounded-2xl shadow-lg md:w-2/5 overflow-hidden flex-col">
                <AiTutorPanel
                  onCollapse={() => {
                    setIsAiTutorVisible(false);
                    setUnreadMessages(0);
                  }}
                  onNewMessage={(count) => setUnreadMessages(prev => prev + count)}
                  onAddTranslation={(callback) => {
                    addTranslationRef.current = callback;
                  }}
                  onAiMessage={(message) => {
                    // Store the message for notification when panel is collapsed
                    if (!isAiTutorVisible) {
                      setAiTutorNotification(message);
                    }
                  }}
                />
              </div>
            </>
          )}

          {/* AI Tutor toggle button */}
          {!isAiTutorVisible && allowAiTutor && (
            <div className="fixed right-4 bottom-4 z-50">
              <button
                onClick={toggleAiTutor}
                className="bg-white p-0 rounded-md shadow-lg relative border-2 border-blue-400"
                aria-label="Show AI Tutor"
                style={{ width: '48px', height: '48px' }}
              >
                <AiTutorAvatar size="lg" />
                {unreadMessages > 0 && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-sm">
                    {unreadMessages > 9 ? '9+' : unreadMessages}
                  </div>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Translation menu from useTranslateV2 */}
      {t.menuData && (
        <div
          style={{
            position: 'fixed',
            top: t.menuData.position.top,
            left: t.menuData.position.left,
            zIndex: 1000
          }}
          className="bg-white shadow-md rounded p-2"
          onClick={(e) => e.stopPropagation()}
        >
          {t.menuData.text ? (
            <div className="px-4 py-2 text-gray-800">{t.menuData.text}</div>
          ) : (
            <button
              onClick={t.onTranslate}
              className="text-blue-500 hover:underline px-4 py-2"
            >
              {t.menuData.displayLanguage === 'en' ? 'Translate' :
               t.menuData.displayLanguage === 'ms' ? 'Terjemah' : '翻译'}
            </button>
          )}
        </div>
      )}

      {/* Translation bubble from useTranslateBubble */}
      {translateBubble.isVisible && translateBubble.position && (
        <div
          style={{
            position: 'fixed',
            top: translateBubble.position.top,
            left: translateBubble.position.left,
            transform: 'translate(-50%,-100%)',
            zIndex: 1000
          }}
          className="flex bg-white text-sm rounded-lg shadow-md px-3 py-2 select-none border border-blue-100"
        >
          <button
            className="mx-1 text-blue-600 hover:text-blue-800 font-medium flex items-center"
            onClick={translateBubble.handleTranslate}
            disabled={translateBubble.text === 'Translating...'}
          >
            {translateBubble.text === 'Translating...' ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {displayLanguage === 'en' ? 'Translating...' :
                 displayLanguage === 'ms' ? 'Menterjemah...' : '翻译中...'}
              </span>
            ) : (
              <>
                <span className="mr-1">🌐</span>
                {displayLanguage === 'en' ? 'Translate' :
                 displayLanguage === 'ms' ? 'Terjemah' : '翻译'}
              </>
            )}
          </button>
          <button
            className="ml-2 text-gray-500 hover:text-gray-700"
            onClick={translateBubble.handleClose}
            aria-label="Close"
          >
            ✕
          </button>
        </div>
      )}

      {/* AI Tutor notification - shown to the left of AI tutor button */}
      {aiTutorNotification && !isAiTutorVisible && (
        <AiTutorNotification
          message={aiTutorNotification}
          displayLanguage={displayLanguage}
          onClose={() => setAiTutorNotification(null)}
          onExpand={() => {
            setIsAiTutorVisible(true);
            setAiTutorNotification(null);
            setUnreadMessages(0);
          }}
        />
      )}


    </div>
  );
}
