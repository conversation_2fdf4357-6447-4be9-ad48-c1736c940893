import React, { useState, useEffect } from 'react';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useSWR from 'swr';
import NoteModal from '../../components/admin/NoteModal';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import Link from 'next/link';

interface Year {
  id: number;
  yearNumber: number;
}

interface Subject {
  id: number;
  name: string;
}

interface Unit {
  id: number;
  unitNumber: number;
  topicEn: string;
  topicZh: string;
}

interface Note {
  id: number;
  filename: string;
  fileUrl: string;
  mimeType: string;
  fileSize: number;
  contentType: string;
  yearId: number;
  subjectId: number;
  unitId: number;
  createdAt: string;
  year: {
    id: number;
    yearNumber: number;
  };
  subject: {
    id: number;
    name: string;
  };
  unit: {
    id: number;
    unitNumber: number;
    topicEn: string;
    topicZh: string;
  };
  content?: string;
}

// Define note types for filtering
const NOTE_TYPES = [
  { value: 'PDF', label: 'PDF' },
  { value: 'IMAGE', label: 'Image' },
  { value: 'TEXT', label: 'Text' },
  { value: 'MARKDOWN', label: 'Markdown' },
  { value: 'SAMPLE', label: 'Sample Questions' },
  { value: 'OTHER', label: 'Other' },
];

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then((res) => res.json());

const NotesViewerPage: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();

  // Filter state
  const [selectedYearId, setSelectedYearId] = useState<string>('');
  const [selectedSubjectId, setSelectedSubjectId] = useState<string>('');
  const [selectedUnitId, setSelectedUnitId] = useState<string>('');
  const [selectedNoteTypes, setSelectedNoteTypes] = useState<string[]>([]);

  // Modal state
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);

  // Data state
  const [years, setYears] = useState<Year[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);

  // Build query string for API call
  const buildQueryString = () => {
    const params = new URLSearchParams();

    if (selectedYearId) {
      params.append('yearId', selectedYearId);
    }

    if (selectedSubjectId) {
      params.append('subjectId', selectedSubjectId);
    }

    if (selectedUnitId) {
      params.append('unitId', selectedUnitId);
    }

    if (selectedNoteTypes.length > 0) {
      params.append('kind', selectedNoteTypes.join(','));
    }

    return params.toString();
  };

  // Fetch notes data with SWR
  const { data, error, isLoading } = useSWR(
    `/api/admin/notes?${buildQueryString()}`,
    fetcher
  );

  // Fetch years, subjects, and units for filters
  useEffect(() => {
    const fetchYears = async () => {
      try {
        const response = await fetch('/api/years');
        if (response.ok) {
          const data = await response.json();
          setYears(data);
        }
      } catch (error) {
        console.error('Error fetching years:', error);
      }
    };

    const fetchSubjects = async () => {
      try {
        const response = await fetch('/api/subjects');
        if (response.ok) {
          const data = await response.json();
          setSubjects(data);
        }
      } catch (error) {
        console.error('Error fetching subjects:', error);
      }
    };

    fetchYears();
    fetchSubjects();
  }, []);

  // Fetch units when subject changes
  useEffect(() => {
    const fetchUnits = async () => {
      if (!selectedSubjectId) {
        setUnits([]);
        return;
      }

      try {
        const response = await fetch(`/api/topics?subjectId=${selectedSubjectId}`);
        if (response.ok) {
          const data = await response.json();
          setUnits(data);
        }
      } catch (error) {
        console.error('Error fetching units:', error);
      }
    };

    fetchUnits();
  }, [selectedSubjectId]);

  // Client-side redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'unauthenticated' || (session && session.user?.role !== 'ADMIN')) {
      router.push('/login');
    }
  }, [session, status, router]);

  // Handle note type selection
  const handleNoteTypeChange = (type: string) => {
    setSelectedNoteTypes((prev) => {
      if (prev.includes(type)) {
        return prev.filter((t) => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // Handle note click to open modal
  const handleNoteClick = async (note: Note) => {
    // For markdown notes, try to fetch the content from our API if it's not already available
    if ((note.contentType === 'MARKDOWN' || note.contentType === 'SAMPLE') && !note.content) {
      try {
        console.log(`Fetching content for note ${note.id} from API`);
        const response = await fetch(`/api/admin/note-content/${note.id}`);

        if (response.ok) {
          const data = await response.json();
          console.log(`Successfully fetched content, length: ${data.content.length}`);
          setSelectedNote({...note, content: data.content});
          return;
        } else {
          console.error(`Failed to fetch content from API: HTTP ${response.status}`);

          // Fallback: try to fetch directly from the URL
          try {
            // Check if the URL is a relative path or absolute URL
            const isRelativePath = !note.fileUrl.startsWith('http://') && !note.fileUrl.startsWith('https://');

            if (isRelativePath) {
              // For relative paths, use the current origin
              const origin = window.location.origin;
              const fullUrl = `${origin}${note.fileUrl.startsWith('/') ? note.fileUrl : `/${note.fileUrl}`}`;

              console.log(`Fallback: Fetching from relative path: ${fullUrl}`);
              const directResponse = await fetch(fullUrl);

              if (directResponse.ok) {
                const content = await directResponse.text();
                console.log(`Successfully fetched content from relative path, length: ${content.length}`);
                setSelectedNote({...note, content});
                return;
              } else {
                console.error(`Failed to fetch content from relative path: HTTP ${directResponse.status}`);
              }
            } else {
              // For absolute URLs, fetch directly
              console.log(`Fallback: Fetching from absolute URL: ${note.fileUrl}`);
              const directResponse = await fetch(note.fileUrl);

              if (directResponse.ok) {
                const content = await directResponse.text();
                console.log(`Successfully fetched content from absolute URL, length: ${content.length}`);
                setSelectedNote({...note, content});
                return;
              } else {
                console.error(`Failed to fetch content from absolute URL: HTTP ${directResponse.status}`);
              }
            }
          } catch (directError) {
            console.error('Error fetching note content directly:', directError);
          }
        }
      } catch (error) {
        console.error('Error fetching note content from API:', error);
      }
    }

    // If we couldn't fetch content or it's not a markdown note, just use what we have
    setSelectedNote(note);
  };

  // Close modal
  const handleCloseModal = () => {
    setSelectedNote(null);
  };

  // Get content preview for markdown notes
  const getContentPreview = (note: Note) => {
    if ((note.contentType === 'MARKDOWN' || note.contentType === 'SAMPLE') && note.content) {
      // Format the preview text to preserve line breaks
      const previewText = note.content.slice(0, 200) + (note.content.length > 200 ? '...' : '');
      return (
        <div className="whitespace-pre-wrap">
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkBreaks]}
            components={{
              p: ({node, ...props}) => <p className="whitespace-pre-wrap" {...props} />,
              li: ({node, ...props}) => <li className="whitespace-pre-wrap" {...props} />,
              pre: ({node, ...props}) => <pre className="whitespace-pre-wrap overflow-x-auto" {...props} />,
              code: ({node, ...props}) => <code className="whitespace-pre-wrap" {...props} />
            }}
          >
            {previewText}
          </ReactMarkdown>
        </div>
      );
    }
    return null;
  };

  // Render loading state
  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  // Render content if authenticated
  return (
    <div className="container mx-auto">
      <div className="bg-white shadow-sm mb-6">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-blue-600">QuizApp</div>
            <span className="text-sm bg-purple-100 text-purple-800 px-3 py-1 rounded-full">Admin Portal</span>
          </div>
          <Link href="/admin" className="text-blue-600 hover:text-blue-800">
            ← Back to Admin Dashboard
          </Link>
        </div>
      </div>

      <div className="px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Notes Viewer</h1>

      {/* Filter Bar */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Year Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
            <select
              value={selectedYearId}
              onChange={(e) => setSelectedYearId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Years</option>
              {years.map((year) => (
                <option key={year.id} value={year.id}>
                  {year.yearNumber}
                </option>
              ))}
            </select>
          </div>

          {/* Subject Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <select
              value={selectedSubjectId}
              onChange={(e) => {
                setSelectedSubjectId(e.target.value);
                setSelectedUnitId(''); // Reset unit when subject changes
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Subjects</option>
              {subjects.map((subject) => (
                <option key={subject.id} value={subject.id}>
                  {subject.name}
                </option>
              ))}
            </select>
          </div>

          {/* Unit Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
            <select
              value={selectedUnitId}
              onChange={(e) => setSelectedUnitId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={!selectedSubjectId}
            >
              <option value="">All Units</option>
              {units.map((unit) => (
                <option key={unit.id} value={unit.id}>
                  Unit {unit.unitNumber}: {unit.topicEn}
                </option>
              ))}
            </select>
          </div>

          {/* Note Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Note Type</label>
            <div className="flex flex-wrap gap-2">
              {NOTE_TYPES.map((type) => (
                <label key={type.value} className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedNoteTypes.includes(type.value)}
                    onChange={() => handleNoteTypeChange(type.value)}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 mr-1"
                  />
                  <span className="text-sm text-gray-700">{type.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Notes List */}
      {isLoading ? (
        <div className="text-center py-8">Loading notes...</div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">Error loading notes</div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {data?.notes?.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No notes found matching the selected filters</div>
          ) : (
            data?.notes?.map((note: Note) => (
              <div
                key={note.id}
                className="bg-white rounded-lg shadow overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleNoteClick(note)}
              >
                <div className="flex flex-col md:flex-row">
                  <div className="p-4 md:w-1/5 bg-gray-50 flex flex-col justify-center">
                    <div className="font-medium">{note.year.yearNumber} | {note.subject.name}</div>
                    <div className="text-sm text-gray-500">Unit {note.unit.unitNumber}</div>
                    <div className="text-xs text-gray-400 mt-2">{note.contentType}</div>
                  </div>
                  <div className="p-4 md:w-4/5">
                    {note.contentType === 'MARKDOWN' || note.contentType === 'SAMPLE' ? (
                      <div className="prose prose-sm markdown-preview">
                        <style jsx global>{`
                          .markdown-preview p {
                            margin-bottom: 0.5em;
                            white-space: pre-wrap;
                          }
                          .markdown-preview br {
                            display: block;
                            content: "";
                            margin-top: 0.25em;
                          }
                          .markdown-preview li {
                            white-space: pre-wrap;
                          }
                          .markdown-preview pre {
                            white-space: pre-wrap;
                            overflow-x: auto;
                          }
                          .markdown-preview code {
                            white-space: pre-wrap;
                          }
                        `}</style>
                        {getContentPreview(note)}
                        <div className="text-blue-500 text-sm mt-2">(click to view)</div>
                      </div>
                    ) : note.mimeType.startsWith('image/') ? (
                      <div className="flex items-center">
                        <img src={note.fileUrl} alt={note.filename} className="w-32 h-auto object-contain" />
                        <span className="ml-4 text-sm text-gray-500">{note.filename}</span>
                      </div>
                    ) : note.mimeType === 'application/pdf' ? (
                      <div className="flex items-center">
                        <span className="text-2xl">📄</span>
                        <span className="ml-4">{note.filename}</span>
                      </div>
                    ) : (
                      <div>
                        <span>{note.filename}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Note Modal */}
      {selectedNote && (
        <NoteModal note={selectedNote} onClose={handleCloseModal} />
      )}
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session || session.user?.role !== 'ADMIN') {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

export default NotesViewerPage;
