/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
      "./pages/**/*.{js,ts,jsx,tsx}",
      "./components/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
        maxHeight: {
          '1/2': '50vh',
          '2/5': '40vh',
        },
        animation: {
          'slideUp': 'slideUp 0.3s ease-out forwards',
          'fadeIn': 'fadeIn 0.3s ease-out forwards',
        },
        keyframes: {
          slideUp: {
            '0%': { transform: 'translateY(100%)' },
            '100%': { transform: 'translateY(0)' },
          },
          fadeIn: {
            '0%': { opacity: '0' },
            '100%': { opacity: '1' },
          },
        },
      }
    },
    plugins: [
      require('@tailwindcss/line-clamp'),
    ],
  };
