import React from 'react';
import { useRouter } from 'next/router';

interface QuizResultsProps {
  score: number;
  total: number;
  displayLanguage: 'en' | 'zh' | 'ms';
  onBack: () => void;
  alreadyCompleted?: boolean;
}

/**
 * Component to display quiz results after completion
 */
const QuizResults: React.FC<QuizResultsProps> = ({
  score,
  total,
  displayLanguage,
  onBack,
  alreadyCompleted = false
}) => {
  // Get the appropriate text based on language
  const getTitle = () => {
    if (alreadyCompleted) {
      if (displayLanguage === 'en') return 'Quiz Already Completed';
      if (displayLanguage === 'zh') return '测验已完成';
      return 'Kuiz Sudah Selesai'; // Malay
    } else {
      if (displayLanguage === 'en') return 'Quiz Complete';
      if (displayLanguage === 'zh') return '测验完成';
      return 'Kuiz <PERSON>'; // Malay
    }
  };

  const getMessage = () => {
    if (alreadyCompleted) {
      if (displayLanguage === 'en') return 'You have already completed this quiz.';
      if (displayLanguage === 'zh') return '您已经完成了这个测验。';
      return 'Anda telah menyelesaikan kuiz ini.'; // Malay
    } else {
      if (displayLanguage === 'en') return `Your score: ${score} / ${total}`;
      if (displayLanguage === 'zh') return `你的得分：${score} / ${total}`;
      return `Skor anda: ${score} / ${total}`; // Malay
    }
  };

  const getButtonText = () => {
    if (displayLanguage === 'en') return 'Back to Dashboard';
    if (displayLanguage === 'zh') return '返回仪表板';
    return 'Kembali ke Papan Pemuka'; // Malay
  };

  return (
    <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full text-center">
        <h2 className="text-2xl font-bold mb-4">
          {getTitle()}
        </h2>
        <p className="text-lg mb-6">
          {getMessage()}
        </p>

        {/* Back to Dashboard button */}
        <button
          onClick={onBack}
          className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded focus:outline-none"
        >
          {getButtonText()}
        </button>
      </div>
    </div>
  );
};

export default QuizResults;
