/**
 * API helper functions for the quiz application
 */

/**
 * Subject interface representing a subject from the database
 */
export interface Subject {
  id: number;
  name: string;
}

/**
 * Unit/Topic interface representing a unit from the database
 */
export interface Unit {
  id: number;
  unitNumber: number;
  topicEn: string;
  topicZh: string;
}

/**
 * Quiz attempt creation payload
 */
export interface CreateQuizAttemptPayload {
  mode: 'mastery' | 'test' | 'quick';
  subjectId: number | null;
  unitId: number | null;
  childId?: number; // Optional, will use the authenticated user's child ID if not provided
}

/**
 * Quiz attempt response
 */
export interface QuizAttemptResponse {
  id: number;
  questionIds: string[];
}

/**
 * Fetch all available subjects
 * @returns Promise resolving to an array of subjects
 */
export async function getSubjects(): Promise<Subject[]> {
  try {
    const response = await fetch('/api/subjects');
    if (!response.ok) {
      throw new Error(`Error fetching subjects: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch subjects:', error);
    return [];
  }
}

/**
 * Fetch topics/units for a specific subject
 * @param subjectId The ID of the subject to fetch topics for
 * @returns Promise resolving to an array of units/topics
 */
export async function getTopics(subjectId: number): Promise<Unit[]> {
  try {
    const response = await fetch(`/api/topics?subjectId=${subjectId}`);
    if (!response.ok) {
      throw new Error(`Error fetching topics: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Failed to fetch topics for subject ${subjectId}:`, error);
    return [];
  }
}

/**
 * Create a new quiz attempt
 * @param payload The quiz attempt creation payload
 * @returns Promise resolving to the created quiz attempt
 */
export async function createQuizAttempt(payload: CreateQuizAttemptPayload): Promise<QuizAttemptResponse> {
  try {
    const response = await fetch('/api/quiz-attempts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include credentials for authentication
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      throw new Error(`Error creating quiz attempt: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to create quiz attempt:', error);
    throw error;
  }
}
