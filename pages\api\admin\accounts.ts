import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const accounts = await prisma.account.findMany({
        include: {
          children: true,
          license: true
        }
      });
      res.status(200).json(accounts);
    } catch (error) {
      console.error('Detailed error:', error);
      res.status(500).json({ error: 'Failed to fetch accounts' });
    }
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}