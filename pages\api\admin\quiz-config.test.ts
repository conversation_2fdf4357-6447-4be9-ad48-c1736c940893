import { createMocks } from 'node-mocks-http';
import handler from './quiz-config';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';

// Mock the next-auth session
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock the Prisma client
jest.mock('../../../lib/prisma', () => ({
  quizConfig: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
  },
}));

describe('Admin Quiz Config API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock admin session
  const mockAdminSession = {
    user: {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  };

  // Mock non-admin session
  const mockNonAdminSession = {
    user: {
      id: '2',
      name: 'Regular User',
      email: '<EMAIL>',
      role: 'PARENT',
    },
  };

  describe('GET method', () => {
    it('should return 401 if not authenticated', async () => {
      // Mock unauthenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Unauthorized' });
    });

    it('should return 403 if not an admin', async () => {
      // Mock non-admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockNonAdminSession);

      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(403);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Forbidden - Admin access required' });
    });

    it('should return all quiz configs when no mode is specified', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock Prisma response
      const mockConfigs = [
        { id: 1, mode: 'MASTERY', numQuestions: 10 },
        { id: 2, mode: 'TEST', numQuestions: 20 },
      ];
      (prisma.quizConfig.findMany as jest.Mock).mockResolvedValueOnce(mockConfigs);

      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(mockConfigs);
    });

    it('should return a specific quiz config when mode is specified', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock Prisma response
      const mockConfig = { id: 1, mode: 'MASTERY', numQuestions: 10 };
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(mockConfig);

      const { req, res } = createMocks({
        method: 'GET',
        query: { mode: 'MASTERY' },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(mockConfig);
    });

    it('should return 404 if the specified mode is not found', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock Prisma response (not found)
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'GET',
        query: { mode: 'NONEXISTENT' },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Quiz config for mode NONEXISTENT not found' });
    });
  });

  describe('PUT method', () => {
    it('should update a quiz config and return the updated config', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock existing config
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce({ id: 1, mode: 'MASTERY' });

      // Mock update response
      const updatedConfig = {
        id: 1,
        mode: 'MASTERY',
        numQuestions: 15,
        allowHints: false,
      };
      (prisma.quizConfig.update as jest.Mock).mockResolvedValueOnce(updatedConfig);

      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          mode: 'MASTERY',
          numQuestions: 15,
          allowHints: false,
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(updatedConfig);
    });

    it('should return 400 if mode is missing', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          numQuestions: 15,
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Quiz mode is required' });
    });

    it('should return 404 if the config to update does not exist', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock non-existent config
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'PUT',
        body: {
          mode: 'NONEXISTENT',
          numQuestions: 15,
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Quiz config for mode NONEXISTENT not found' });
    });
  });

  it('should return 405 for unsupported methods', async () => {
    // Mock admin session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

    const { req, res } = createMocks({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Method not allowed' });
  });
});
