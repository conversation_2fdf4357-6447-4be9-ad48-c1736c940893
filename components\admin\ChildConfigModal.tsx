import React, { useState } from 'react';
import { Language } from '@prisma/client';

interface Child {
  id: number;
  name: string;
  year: string;
  username: string;
  quizLanguage?: Language;
  menuLanguage?: Language;
  showDualLanguage?: boolean;
}

interface ChildConfigModalProps {
  child: Child;
  onClose: () => void;
  onSave: (childId: number, data: any) => Promise<void>;
}

const ChildConfigModal: React.FC<ChildConfigModalProps> = ({ child, onClose, onSave }) => {
  const [name, setName] = useState(child.name);
  const [year, setYear] = useState(child.year);
  const [quizLanguage, setQuizLanguage] = useState<Language>(child.quizLanguage || Language.ZH);
  const [menuLanguage, setMenuLanguage] = useState<Language>(child.menuLanguage || Language.EN);
  const [showDualLanguage, setShowDualLanguage] = useState<boolean>(child.showDualLanguage || false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await onSave(child.id, {
        name,
        year,
        quizLanguage,
        menuLanguage,
        showDualLanguage
      });
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving changes');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Edit Child: {child.username}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Year
              </label>
              <select
                value={year}
                onChange={(e) => setYear(e.target.value)}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                {['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5', 'Year 6'].map((y) => (
                  <option key={y} value={y}>{y}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quiz Language (Questions and Answers)
              </label>
              <select
                value={quizLanguage}
                onChange={(e) => setQuizLanguage(e.target.value as Language)}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={Language.ZH}>Chinese (中文)</option>
                <option value={Language.EN}>English</option>
                <option value={Language.MS}>Malay</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Menu Language (UI Elements)
              </label>
              <select
                value={menuLanguage}
                onChange={(e) => setMenuLanguage(e.target.value as Language)}
                className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={Language.EN}>English</option>
                <option value={Language.ZH}>Chinese (中文)</option>
                <option value={Language.MS}>Malay</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="showDualLanguage"
                checked={showDualLanguage}
                onChange={(e) => setShowDualLanguage(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="showDualLanguage" className="ml-2 block text-sm text-gray-700">
                Show questions in dual language (Quiz Language + Menu Language)
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChildConfigModal;
