import fs from 'fs/promises';
import path from 'path';
import { PrismaClient, QuestionType, AnswerType, Language } from '@prisma/client';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { buildPrompt } from '../templates/generationPrompt';

// Initialize Prisma client
const prisma = new PrismaClient();

// Define BatchStatus enum to match Prisma schema
enum BatchStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// Define the queue directory
const queueDir = path.join(process.cwd(), 'queue');

// Define the type for the queue file content
interface QueueFile {
  batchId: number;
  provider?: string; // 'openrouter' or 'gemini'
  language?: Language; // Optional for backward compatibility
  tpDistribution?: number[]; // Optional TP level distribution
}

// Define the type for the OpenRouter API response
interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
    finish_reason?: string;
    native_finish_reason?: string;
    index?: number;
    logprobs?: any;
  }[];
  error?: {
    message: string;
    code: number;
    metadata?: {
      raw?: string;
      provider_name?: string;
    };
  };
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  id?: string;
  provider?: string;
  model?: string;
  object?: string;
  created?: number;
}

// Define the type for the Gemini API response
interface GeminiResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
      role: string;
    };
    finishReason: string;
    index: number;
  }[];
  error?: {
    message: string;
    code: number;
  };
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
    promptTokensDetails?: {
      modality: string;
      tokenCount: number;
    }[];
    thoughtsTokenCount?: number;
  };
  modelVersion?: string;
}

// Define the type for a generated question
interface GeneratedQuestion {
  id: string;
  type: QuestionType;
  promptEn: string;
  promptZh: string;
  promptMs?: string;
  spec?: any;
  tpLevel?: number; // Tahap Penguasaan level (1-6)
  keywords?: {
    en?: string[];
    zh?: string[];
    ms?: string[];
  };
  choices?: {
    key: string;
    textEn: string;
    textZh: string;
    textMs?: string;
    mediaUrl?: string;
  }[];
  answer: {
    key?: string;
    textEn?: string;
    textZh?: string;
    textMs?: string;
    answerSpec?: any;
  };
  explanation: {
    textEn: string;
    textZh: string;
    textMs?: string;
  };
}

/**
 * Maps question types to their corresponding answer types
 */
function mapAnswerType(type: QuestionType): AnswerType {
  switch(type) {
    case 'MULTIPLE_CHOICE':
    case 'MULTIPLE_CHOICE_IMAGE':
    case 'PICTURE_PROMPT':
    case 'TRUE_FALSE':
      return 'SINGLE_CHOICE';
    case 'SHORT_ANSWER':
      return 'SHORT_TEXT';
    case 'LONG_ANSWER':
      return 'LONG_TEXT_RUBRIC';
    case 'FILL_IN_THE_BLANK':
      return 'FILL_IN_THE_BLANK';
    case 'MATCHING':
      return 'MATCHING';
    case 'SEQUENCING':
      return 'SEQUENCING';
    default:
      return 'SINGLE_CHOICE';
  }
}

/**
 * Fetches the content of a note from storage
 */
async function fetchNoteContent(fileUrl: string): Promise<string> {
  try {
    // Remove the leading slash from the URL
    const key = fileUrl.startsWith('/uploads/')
      ? fileUrl.substring('/uploads/'.length)
      : fileUrl;

    // Get the file path
    const filePath = path.join(process.cwd(), 'uploads', key);

    // Read the file content
    const content = await fs.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    console.error(`Error fetching note content: ${error}`);
    return '';
  }
}

/**
 * Processes a single generation batch
 */
async function processBatch(
  batchId: number,
  queueProvider?: string,
  queueLanguage?: Language,
  tpDistribution?: number[]
): Promise<void> {
  console.log(`Processing batch ${batchId}...`);

  try {
    // Update batch status to IN_PROGRESS
    await prisma.generationBatch.update({
      where: { id: batchId },
      data: { status: BatchStatus.IN_PROGRESS }
    });

    // Fetch the batch with its notes (including both MARKDOWN and SAMPLE notes)
    const batch = await prisma.generationBatch.findUnique({
      where: { id: batchId },
      include: {
        notes: {
          where: {
            contentType: {
              in: ['MARKDOWN', 'TEXT', 'SAMPLE']
            }
          }
        },
        year: true,
        subject: true,
        unit: true
      }
    });

    if (!batch) {
      console.error(`Batch ${batchId} not found`);
      return;
    }

    // Separate notes into sample questions and regular notes
    const sampleNotes = batch.notes.filter(note => note.contentType === 'SAMPLE');
    const regularNotes = batch.notes.filter(note => note.contentType !== 'SAMPLE');

    // Fetch the content of sample notes
    const sampleContents = await Promise.all(
      sampleNotes.map(async (note) => {
        if (note.contentType === 'SAMPLE') {
          return await fetchNoteContent(note.fileUrl);
        }
        return `[SAMPLE ${note.contentType} file: ${note.filename}]`;
      })
    );

    // Fetch the content of regular notes
    const regularContents = await Promise.all(
      regularNotes.map(async (note) => {
        if (note.contentType === 'MARKDOWN' || note.contentType === 'TEXT') {
          return await fetchNoteContent(note.fileUrl);
        }
        return `[${note.contentType} file: ${note.filename}]`;
      })
    );

    // Combine the note contents with appropriate headers
    let context = '';

    // Add sample questions if available
    if (sampleContents.length > 0) {
      context += '### Sample Questions\n\n';
      context += sampleContents.join('\n\n');
      context += '\n\n';
    }

    // Add regular notes
    if (regularContents.length > 0) {
      context += '### Lesson Notes\n\n';
      context += regularContents.join('\n\n');
    }

    // Get the language from the batch or use the queue language parameter
    const generationLanguage = queueLanguage || batch.language || Language.ZH;
    console.log(`Using language: ${generationLanguage} for batch ${batchId}`);

    // Get the provider from the batch or use the queue provider parameter
    const provider = queueProvider || batch.provider || 'openrouter';
    console.log(`Using provider: ${provider} with model ${batch.modelUsed} for batch ${batchId}`);

    // For Gemini, limit the number of questions to reduce the chance of truncation
    const adjustedNumQuestions = provider === 'gemini' && batch.numQuestions > 5 ? 5 : batch.numQuestions;

    if (provider === 'gemini' && batch.numQuestions > 5) {
      console.log(`Limiting Gemini request to ${adjustedNumQuestions} questions to avoid truncation (original: ${batch.numQuestions})`);
    }

    // Create the prompt for the AI using the template
    const prompt = buildPrompt({
      num: adjustedNumQuestions,
      types: batch.questionTypes,
      year: batch.year.yearNumber,
      subject: batch.subject.name,
      unit: batch.unit ? batch.unit.topicEn : undefined,
      notes: context,
      language: generationLanguage,
      tpDistribution: tpDistribution ? tpDistribution.slice(0, adjustedNumQuestions) : undefined // Adjust TP distribution if needed
    });

    // Log more details about the batch for debugging
    console.log(`Batch details:
      ID: ${batch.id}
      Provider: ${batch.provider}
      Model: ${batch.modelUsed}
      Language: ${batch.language}
      Status: ${batch.status}
      TP Distribution: ${tpDistribution}
    `);

    // Maximum number of retries for rate limit errors
    const maxRetries = 3;
    let retryCount = 0;
    let response: any; // Using any for node-fetch Response
    let json: GeminiResponse | OpenRouterResponse; // Will hold the API response

    while (retryCount <= maxRetries) {
      try {
        // Different API call based on provider
        if (provider === 'gemini') {
          // Call Gemini API
          // Extract the model name from batch.modelUsed
          const geminiModel = batch.modelUsed;
          console.log(`Calling Gemini API with model ${geminiModel}...`);

          response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-goog-api-key': process.env.GEMINI_API_KEY || ''
            },
            body: JSON.stringify({
              contents: [
                {
                  role: 'user',
                  parts: [
                    {
                      text: 'You are an expert educational question generator. Return ONLY raw JSON without any markdown formatting, explanation, or code blocks. DO NOT USE MARKDOWN CODE BLOCKS (```) AROUND YOUR RESPONSE. DO NOT PREFIX YOUR RESPONSE WITH "json" OR ANY OTHER TEXT. Your entire response must be a valid JSON array that can be parsed directly with JSON.parse(). The response should start with [ and end with ] without any other characters before or after.'
                    },
                    {
                      text: prompt
                    }
                  ]
                }
              ],
              generationConfig: {
                temperature: 0.3,
                maxOutputTokens: 8192,
                topP: 0.95
              }
            })
          });
        } else {
          // Default to OpenRouter API
          console.log(`Calling OpenRouter API with model ${batch.modelUsed}...`);

          response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${process.env.OPENROUTER_GENERATE_QUESTIONS_API_KEY}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': 'https://my-quiz-app.example.com', // Replace with your actual domain
              'X-Title': 'My Quiz App'
            },
            body: JSON.stringify({
              model: batch.modelUsed,
              messages: [
                {
                  role: 'system',
                  content: 'You are an expert educational question generator. Return ONLY raw JSON without any markdown formatting, explanation, or code blocks. DO NOT USE MARKDOWN CODE BLOCKS (```) AROUND YOUR RESPONSE. DO NOT PREFIX YOUR RESPONSE WITH "json" OR ANY OTHER TEXT. Your entire response must be a valid JSON array that can be parsed directly with JSON.parse(). The response should start with [ and end with ] without any other characters before or after.'
                },
                { role: 'user', content: prompt }
              ],
              temperature: 0.3, // Lower temperature for more deterministic output
              max_tokens: 20000
            })
          });
        }

        // If response is not OK, check if it's a rate limit error
        if (!response.ok) {
          const errorText = await response.text();
          let errorJson: any;

          try {
            errorJson = JSON.parse(errorText);
          } catch (e) {
            // If we can't parse the error as JSON, just throw the original error
            throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
          }

          // Check if it's a rate limit error (code 429)
          if (errorJson.error && errorJson.error.code === 429) {
            retryCount++;
            if (retryCount <= maxRetries) {
              console.log(`Rate limit error detected. Retry attempt ${retryCount} of ${maxRetries}`);
              console.log(`Error details: ${errorJson.error.metadata?.raw || errorJson.error.message}`);
              console.log(`Waiting for 60 seconds before retrying...`);

              // Wait for 1 minute (60000 ms) before retrying
              await new Promise(resolve => setTimeout(resolve, 60000));
              continue;
            }
          }

          // If it's not a rate limit error or we've exceeded retries, throw the error
          throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
        }

        // If we get here, the response was successful
        if (provider === 'gemini') {
          json = await response.json() as GeminiResponse;
          console.log('Gemini raw response:', JSON.stringify(json, null, 2));
        } else {
          json = await response.json() as OpenRouterResponse;
          console.log('OpenRouter raw response:', JSON.stringify(json, null, 2));
        }

        // Check for errors in the response based on provider
        if (provider === 'gemini') {
          // Handle Gemini-specific errors
          const geminiJson = json as GeminiResponse;
          if (geminiJson.error) {
            // Check if it's a rate limit error
            if (geminiJson.error.code === 429) {
              retryCount++;
              if (retryCount <= maxRetries) {
                console.log(`Gemini rate limit error detected. Retry attempt ${retryCount} of ${maxRetries}`);
                console.log(`Error details: ${geminiJson.error.message || 'Unknown error'}`);
                console.log(`Waiting for 60 seconds before retrying...`);

                // Wait for 1 minute (60000 ms) before retrying
                await new Promise(resolve => setTimeout(resolve, 60000));
                continue;
              } else {
                throw new Error(`Gemini rate limit exceeded after ${maxRetries} retries: ${geminiJson.error.message}`);
              }
            } else {
              // Other error in the Gemini response
              throw new Error(`Gemini API error in response: ${geminiJson.error.message}`);
            }
          }

          // Verify that we have candidates in the Gemini response
          if (!geminiJson.candidates || !geminiJson.candidates.length) {
            throw new Error(`Gemini API returned no candidates in the response`);
          }
        } else {
          // Handle OpenRouter-specific errors
          const openRouterJson = json as OpenRouterResponse;
          if (openRouterJson.error) {
            // Check if it's a rate limit error
            if (openRouterJson.error.code === 429) {
              retryCount++;
              if (retryCount <= maxRetries) {
                console.log(`OpenRouter rate limit error detected. Retry attempt ${retryCount} of ${maxRetries}`);
                console.log(`Error details: ${openRouterJson.error.metadata?.raw || openRouterJson.error.message}`);
                console.log(`Waiting for 60 seconds before retrying...`);

                // Wait for 1 minute (60000 ms) before retrying
                await new Promise(resolve => setTimeout(resolve, 60000));
                continue;
              } else {
                throw new Error(`OpenRouter rate limit exceeded after ${maxRetries} retries: ${openRouterJson.error.message}`);
              }
            } else {
              // Other error in the OpenRouter response
              throw new Error(`OpenRouter API error in response: ${openRouterJson.error.message}`);
            }
          }

          // Verify that we have choices in the OpenRouter response
          if (!openRouterJson.choices || !openRouterJson.choices.length) {
            throw new Error(`OpenRouter API returned no choices in the response`);
          }
        }

        // Break out of the retry loop on success
        break;
      } catch (error) {
        // If this is not the last retry, continue to the next iteration
        if ((error.message.includes('rate-limited') ||
             error.message.includes('rate limit') ||
             error.message.includes('429')) &&
            retryCount < maxRetries) {
          retryCount++;
          console.log(`${provider === 'gemini' ? 'Gemini' : 'OpenRouter'} rate limit error caught. Retry attempt ${retryCount} of ${maxRetries}`);
          console.log(`Error details: ${error.message}`);
          console.log(`Waiting for 60 seconds before retrying...`);
          await new Promise(resolve => setTimeout(resolve, 60000));
          continue;
        }

        // Otherwise, rethrow the error
        throw error;
      }
    }

    // Log the prompt and raw response for troubleshooting
    await logGenerationData(
      parseInt(batchId), 
      prompt, 
      provider === 'gemini' ? (json as GeminiResponse) : (json as OpenRouterResponse)
    );

    // Parse the response
    let questions:  GeneratedQuestion[];
    try {
      // Extract content based on provider
      let content = '';
      let isTruncated = false;

      if (provider === 'gemini') {
        // Extract content from Gemini response
        const geminiJson = json as GeminiResponse;
        if (!geminiJson.candidates || !geminiJson.candidates.length || !geminiJson.candidates[0].content) {
          throw new Error('No valid candidates in the Gemini response');
        }

        // Get the text from the first part of the first candidate
        const parts = geminiJson.candidates[0].content.parts;
        if (!parts || !parts.length) {
          throw new Error('No parts in the Gemini response');
        }

        content = parts[0].text || '';

        // Check if response was truncated
        if (geminiJson.candidates[0].finishReason === 'MAX_TOKENS') {
          console.log('WARNING: Gemini response was truncated due to token limit');
          isTruncated = true;
        }
      } else {
        // Extract content from OpenRouter response
        const openRouterJson = json as OpenRouterResponse;
        if (!openRouterJson.choices || !openRouterJson.choices.length || !openRouterJson.choices[0].message) {
          throw new Error('No valid choices in the OpenRouter response');
        }

        content = openRouterJson.choices[0].message.content;

        // Check if response was truncated
        if (openRouterJson.choices[0].finish_reason === 'length' ||
            openRouterJson.choices[0].native_finish_reason === 'MAX_TOKENS') {
          console.log('WARNING: OpenRouter response was truncated due to token limit');
          isTruncated = true;
        }
      }

      // If the response was truncated, try to fix the JSON
      if (isTruncated) {
        console.log('Attempting to fix truncated JSON response...');

        // First approach: Try to extract complete objects using regex
        // This regex matches from the start of the array to the last complete object
        const completeArrayMatch = content.match(/\[\s*(\{[\s\S]*?\})\s*(?:,\s*\{[\s\S]*?\})*\s*/);

        if (completeArrayMatch) {
          // Extract the content up to the last complete object and add closing bracket
          let fixedContent = completeArrayMatch[0];
          if (!fixedContent.trim().endsWith(']')) {
            fixedContent += ']';
          }

          console.log('Extracted complete objects from truncated response');
          console.log('Fixed content (first 100 chars):', fixedContent.substring(0, 100) + '...');

          try {
            // Try to parse the fixed content
            const parsedContent = JSON.parse(fixedContent);
            if (Array.isArray(parsedContent) && parsedContent.length > 0) {
              console.log(`Successfully extracted ${parsedContent.length} complete questions from truncated response`);
              content = fixedContent;
            }
          } catch (fixError) {
            console.error('Failed to parse fixed content with regex approach:', fixError.message);

            // Second approach: Try to manually extract complete objects
            console.log('Trying manual extraction of complete objects...');

            // Find the position of the opening bracket
            const startPos = content.indexOf('[');
            if (startPos !== -1) {
              // Initialize variables to track our position and nesting level
              let pos = startPos + 1;
              let objectStart = -1;
              let objectEnd = -1;
              let braceCount = 0;
              let inString = false;
              let escapeNext = false;
              let objects = [];

              // Scan through the content character by character
              while (pos < content.length) {
                const char = content[pos];

                // Handle string literals and escaping
                if (char === '"' && !escapeNext) {
                  inString = !inString;
                } else if (char === '\\' && !escapeNext) {
                  escapeNext = true;
                } else {
                  escapeNext = false;

                  // Only count braces when not in a string
                  if (!inString) {
                    if (char === '{') {
                      if (braceCount === 0) {
                        objectStart = pos;
                      }
                      braceCount++;
                    } else if (char === '}') {
                      braceCount--;
                      if (braceCount === 0) {
                        objectEnd = pos;
                        // We found a complete object, add it to our list
                        objects.push(content.substring(objectStart, objectEnd + 1));
                      }
                    }
                  }
                }

                pos++;
              }

              if (objects.length > 0) {
                console.log(`Found ${objects.length} complete objects in truncated response`);

                // Construct a valid JSON array from the complete objects
                const reconstructedArray = '[' + objects.join(',') + ']';

                try {
                  // Try to parse the reconstructed array
                  const parsedArray = JSON.parse(reconstructedArray);
                  if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                    console.log(`Successfully reconstructed array with ${parsedArray.length} questions`);
                    content = reconstructedArray;
                  }
                } catch (reconstructError) {
                  console.error('Failed to parse reconstructed array:', reconstructError.message);
                  // Continue with original content and let the regular parsing logic handle it
                }
              }
            }
          }
        } else {
          console.log('No complete objects found with regex, trying manual extraction...');

          // Try the manual extraction approach directly
          // Find the position of the opening bracket
          const startPos = content.indexOf('[');
          if (startPos !== -1) {
            // Initialize variables to track our position and nesting level
            let pos = startPos + 1;
            let objectStart = -1;
            let objectEnd = -1;
            let braceCount = 0;
            let inString = false;
            let escapeNext = false;
            let objects = [];

            // Scan through the content character by character
            while (pos < content.length) {
              const char = content[pos];

              // Handle string literals and escaping
              if (char === '"' && !escapeNext) {
                inString = !inString;
              } else if (char === '\\' && !escapeNext) {
                escapeNext = true;
              } else {
                escapeNext = false;

                // Only count braces when not in a string
                if (!inString) {
                  if (char === '{') {
                    if (braceCount === 0) {
                      objectStart = pos;
                    }
                    braceCount++;
                  } else if (char === '}') {
                    braceCount--;
                    if (braceCount === 0) {
                      objectEnd = pos;
                      // We found a complete object, add it to our list
                      objects.push(content.substring(objectStart, objectEnd + 1));
                    }
                  }
                }
              }

              pos++;
            }

            if (objects.length > 0) {
              console.log(`Found ${objects.length} complete objects in truncated response`);

              // Construct a valid JSON array from the complete objects
              const reconstructedArray = '[' + objects.join(',') + ']';

              try {
                // Try to parse the reconstructed array
                const parsedArray = JSON.parse(reconstructedArray);
                if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                  console.log(`Successfully reconstructed array with ${parsedArray.length} questions`);
                  content = reconstructedArray;
                }
              } catch (reconstructError) {
                console.error('Failed to parse reconstructed array:', reconstructError.message);
                // Continue with original content and let the regular parsing logic handle it
              }
            }
          }
        }
      }

      // Log the raw content for debugging
      console.log('Raw content from model:', content.substring(0, 100) + '...');

      // Remove any "json" prefix that might be present
      if (content.trim().startsWith('json')) {
        console.log('Detected "json" prefix, removing it...');
        content = content.trim().substring(4).trim();
      }

      // Handle case where the model returns markdown code blocks
      if (content.includes('```json') || content.includes('```')) {
        console.log('Detected markdown code block in response, extracting JSON...');

        // Extract content between code blocks - improved regex to handle multiple code blocks
        // This will match the content between the first set of triple backticks
        const jsonMatch = content.match(/```(?:json)?\s*([\s\S]*?)```/);
        if (jsonMatch && jsonMatch[1]) {
          content = jsonMatch[1].trim();
          console.log('Extracted JSON from code block');

          // Log the first 100 characters of the extracted content for debugging
          console.log('Extracted content (first 100 chars):', content.substring(0, 100) + '...');
        } else {
          console.log('Could not extract JSON from code block using regex, trying alternative extraction');

          // Alternative extraction method: remove all occurrences of ```json and ```
          if (content.includes('```json')) {
            content = content.replace(/```json/g, '').replace(/```/g, '').trim();
            console.log('Removed markdown delimiters, new content (first 100 chars):', content.substring(0, 100) + '...');
          } else if (content.includes('```')) {
            content = content.replace(/```/g, '').trim();
            console.log('Removed triple backticks, new content (first 100 chars):', content.substring(0, 100) + '...');
          }
        }
      }

      // Clean up the content - remove any leading/trailing whitespace and newlines
      content = content.trim();

      // Handle the case where there are extra newlines in the JSON
      content = content.replace(/\n\s*\n/g, '\n');

      // Try to parse the JSON
      try {
        questions = JSON.parse(content);
        console.log('Successfully parsed JSON on first attempt');
      } catch (parseError) {
        console.error('JSON parse error:', parseError.message);
        console.log('Content that failed to parse (first 100 chars):', content.substring(0, 100));

        // Additional fallback: Try to extract anything that looks like a JSON array
        console.log('Initial JSON parsing failed, trying to extract JSON array pattern...');

        // Improved regex to match a JSON array - more robust pattern matching
        // This will match from the first [ to the last ] in the content
        const arrayMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
        if (arrayMatch) {
          console.log('Found JSON array pattern, attempting to parse...');
          try {
            questions = JSON.parse(arrayMatch[0]);
            console.log('Successfully parsed extracted JSON array');
          } catch (innerError) {
            console.error('Failed to parse extracted array pattern:', innerError.message);

            // Try a more aggressive approach - find the first [ and last ]
            console.log('Trying more aggressive extraction...');
            const firstBracket = content.indexOf('[');
            const lastBracket = content.lastIndexOf(']');

            if (firstBracket !== -1 && lastBracket !== -1 && firstBracket < lastBracket) {
              const extractedContent = content.substring(firstBracket, lastBracket + 1);
              console.log('Extracted content from first [ to last ] (first 100 chars):',
                extractedContent.substring(0, 100) + '...');

              try {
                questions = JSON.parse(extractedContent);
                console.log('Successfully parsed content extracted from brackets');
              } catch (bracketError) {
                console.error('Failed to parse content extracted from brackets:', bracketError.message);

                // Last resort: Try to manually fix common JSON formatting issues
                console.log('Attempting to fix JSON formatting issues...');
                let fixedContent = extractedContent
                  .replace(/\n/g, ' ')         // Replace newlines with spaces
                  .replace(/\s+/g, ' ')        // Normalize whitespace
                  .replace(/"\s+:/g, '":')     // Fix "  : issues
                  .replace(/:\s+"/g, ':"')     // Fix :  " issues
                  .replace(/,\s*}/g, '}')      // Fix trailing commas
                  .replace(/,\s*]/g, ']')      // Fix trailing commas in arrays
                  .replace(/\\"/g, '"')        // Fix escaped quotes
                  .replace(/"\{/g, '{')        // Fix "{
                  .replace(/\}"/g, '}')        // Fix }"
                  .replace(/"\[/g, '[')        // Fix "[
                  .replace(/\]"/g, ']');       // Fix ]"

                try {
                  questions = JSON.parse(fixedContent);
                  console.log('Successfully parsed JSON after fixing formatting issues');
                } catch (finalError) {
                  console.error('All parsing attempts failed:', finalError.message);

                  // One last attempt - try to manually construct a valid JSON array
                  console.log('Attempting to manually extract individual questions...');

                  // Look for objects that start with {"schemaVersion"
                  const questionRegex = /\{\s*"schemaVersion"\s*:\s*"[^"]+"\s*,[\s\S]*?\}\s*(?=,|\]|$)/g;
                  const matches = extractedContent.match(questionRegex);

                  if (matches && matches.length > 0) {
                    console.log(`Found ${matches.length} potential question objects`);

                    // Try to parse each object individually and build an array
                    const validQuestions = [];
                    for (let i = 0; i < matches.length; i++) {
                      try {
                        // Make sure the object ends with }
                        let questionStr = matches[i].trim();
                        if (!questionStr.endsWith('}')) {
                          questionStr += '}';
                        }

                        const question = JSON.parse(questionStr);
                        validQuestions.push(question);
                      } catch (objError) {
                        console.error(`Failed to parse question ${i+1}:`, objError.message);
                      }
                    }

                    if (validQuestions.length > 0) {
                      console.log(`Successfully parsed ${validQuestions.length} individual questions`);
                      questions = validQuestions;
                    } else {
                      throw parseError; // Throw the original error if no valid questions
                    }
                  } else {
                    throw parseError; // Throw the original error if no matches
                  }
                }
              }
            } else {
              // Last resort for the original content
              console.log('Attempting to fix JSON formatting issues on original content...');
              let fixedContent = content
                .replace(/\n/g, ' ')         // Replace newlines with spaces
                .replace(/\s+/g, ' ')        // Normalize whitespace
                .replace(/"\s+:/g, '":')     // Fix "  : issues
                .replace(/:\s+"/g, ':"')     // Fix :  " issues
                .replace(/,\s*}/g, '}')      // Fix trailing commas
                .replace(/,\s*]/g, ']')      // Fix trailing commas in arrays
                .replace(/\\"/g, '"')        // Fix escaped quotes
                .replace(/"\{/g, '{')        // Fix "{
                .replace(/\}"/g, '}')        // Fix }"
                .replace(/"\[/g, '[')        // Fix "[
                .replace(/\]"/g, ']');       // Fix ]"

              try {
                questions = JSON.parse(fixedContent);
                console.log('Successfully parsed JSON after fixing formatting issues');
              } catch (finalError) {
                console.error('All parsing attempts failed:', finalError.message);
                throw parseError; // Throw the original error
              }
            }
          }
        } else {
          // Try a more aggressive approach even if the regex didn't match
          console.log('No JSON array pattern found, trying direct bracket extraction...');
          const firstBracket = content.indexOf('[');
          const lastBracket = content.lastIndexOf(']');

          if (firstBracket !== -1 && lastBracket !== -1 && firstBracket < lastBracket) {
            const extractedContent = content.substring(firstBracket, lastBracket + 1);
            console.log('Extracted content from first [ to last ] (first 100 chars):',
              extractedContent.substring(0, 100) + '...');

            try {
              questions = JSON.parse(extractedContent);
              console.log('Successfully parsed content extracted from brackets');
            } catch (bracketError) {
              console.error('Failed to parse content extracted from brackets:', bracketError.message);
              throw parseError; // Throw the original error
            }
          } else {
            // Re-throw the original error if we couldn't find brackets
            throw parseError;
          }
        }
      }

      if (!Array.isArray(questions)) {
        throw new Error('Response is not an array');
      }

      console.log(`Successfully parsed ${questions.length} questions`);
    } catch (error) {
      console.error(`Error parsing questions: ${error}`);
      throw new Error(`Failed to parse questions: ${error}`);
    }

    // Create the questions in the database
    for (const q of questions) {
      // Always generate a new unique questionId to avoid conflicts
      // This ensures we don't get unique constraint errors
      const questionId = `q-${uuidv4()}`;

      // Handle PICTURE_PROMPT media first if it exists
      let promptMediaId = null;
      if (q.type === 'PICTURE_PROMPT' && q.spec?.promptImageUrl) {
        const media = await prisma.media.create({
          data: {
            url: q.spec.promptImageUrl,
            altEn: q.spec?.altTextEn || '',
            altZh: q.spec?.altTextZh || ''
          }
        });
        promptMediaId = media.id;
      }

      // Create the question with spec, keywords, and tpLevel
      const question = await prisma.question.create({
        data: {
          questionId,
          type: q.type,
          promptEn: q.promptEn,
          promptZh: q.promptZh,
          promptMs: q.promptMs,
          promptMediaId,
          spec: q.spec ?? null,
          keywords: q.keywords ?? null, // Add keywords field
          tpLevel: q.tpLevel || Math.floor(Math.random() * 6) + 1, // Use provided tpLevel or generate random 1-6
          subjectId: batch.subjectId,
          yearId: batch.yearId,
          unitId: batch.unitId,
          status: 'DRAFT',
          generationBatchId: batch.id,
          subTopicEn: batch.unit?.topicEn || '',
          subTopicZh: batch.unit?.topicZh || '',
          originalLanguage: generationLanguage, // Set based on the selected language
          translationState: 'PARTIAL'
        }
      });

      // Handle TRUE_FALSE questions
      if (q.type === 'TRUE_FALSE') {
        // Create default choices if not provided
        if (!q.choices || q.choices.length === 0) {
          await prisma.choice.createMany({
            data: [
              {
                key: 'TRUE',
                textEn: 'True',
                textZh: '正确',
                questionId: question.id
              },
              {
                key: 'FALSE',
                textEn: 'False',
                textZh: '错误',
                questionId: question.id
              }
            ]
          });
        }

        // Ensure the answer key is either TRUE or FALSE
        if (q.answer && q.answer.key) {
          // Normalize the answer key to TRUE or FALSE
          const normalizedKey = q.answer.key.toUpperCase().trim();
          if (normalizedKey !== 'TRUE' && normalizedKey !== 'FALSE') {
            // If the key is not valid, try to interpret it
            let correctKey: 'TRUE' | 'FALSE';
            if (['T', 'Y', 'YES', '1', 'CORRECT', 'RIGHT'].includes(normalizedKey)) {
              correctKey = 'TRUE';
            } else if (['F', 'N', 'NO', '0', 'INCORRECT', 'WRONG'].includes(normalizedKey)) {
              correctKey = 'FALSE';
            } else {
              // Default to TRUE if we can't determine
              console.warn(`Invalid TRUE_FALSE answer key: "${q.answer.key}". Defaulting to TRUE.`);
              correctKey = 'TRUE';
            }

            // Update the answer with the correct key
            q.answer.key = correctKey;
          }
        } else {
          // If no key is provided, default to TRUE
          console.warn(`Missing answer key for TRUE_FALSE question. Defaulting to TRUE.`);
          if (!q.answer) {
            q.answer = { key: 'TRUE' };
          } else {
            q.answer.key = 'TRUE';
          }
        }
      }
      // Create choices if provided
      else if (q.choices && q.choices.length > 0) {
        // Handle MULTIPLE_CHOICE_IMAGE with mediaUrl
        if (q.type === 'MULTIPLE_CHOICE_IMAGE') {
          // Create media entries first
          for (const choice of q.choices) {
            if (choice.mediaUrl) {
              const media = await prisma.media.create({
                data: {
                  url: choice.mediaUrl
                }
              });

              // Create choice with media
              await prisma.choice.create({
                data: {
                  key: choice.key,
                  textEn: choice.textEn,
                  textZh: choice.textZh,
                  textMs: choice.textMs || null,
                  mediaId: media.id,
                  questionId: question.id
                }
              });
            } else {
              // Create choice without media
              await prisma.choice.create({
                data: {
                  key: choice.key,
                  textEn: choice.textEn,
                  textZh: choice.textZh,
                  textMs: choice.textMs || null,
                  questionId: question.id
                }
              });
            }
          }
        } else {
          // Regular choices without media
          for (const choice of q.choices) {
            await prisma.choice.create({
              data: {
                key: choice.key,
                textEn: choice.textEn,
                textZh: choice.textZh,
                textMs: choice.textMs || null,
                questionId: question.id
              }
            });
          }
        }
      }

      // Create answer with proper type mapping and answerSpec
      if (q.answer) {
        await prisma.answer.create({
          data: {
            questionId: question.id,
            key: q.answer.key,
            textEn: q.answer.textEn,
            textZh: q.answer.textZh,
            textMs: q.answer.textMs || null,
            answerSpec: q.answer.answerSpec ?? q.spec ?? null,
            type: mapAnswerType(q.type)
          }
        });
      }

      // Create explanation
      if (q.explanation) {
        await prisma.explanationText.create({
          data: {
            questionId: question.id,
            textEn: q.explanation.textEn,
            textZh: q.explanation.textZh,
            textMs: q.explanation.textMs || null
          }
        });
      }
    }

    // Extract token usage information based on provider
    let promptTokens = null;
    let completionTokens = null;
    let totalTokens = null;

    if (provider === 'gemini') {
      // Gemini provides usage tokens in a different format
      const geminiJson = json as GeminiResponse;
      if (geminiJson.usageMetadata) {
        promptTokens = geminiJson.usageMetadata.promptTokenCount || null;
        completionTokens = geminiJson.usageMetadata.candidatesTokenCount || null;
        // Use the totalTokenCount directly from the response if available
        totalTokens = geminiJson.usageMetadata.totalTokenCount ||
                     (promptTokens && completionTokens ? promptTokens + completionTokens : null);

        console.log(`Gemini token usage - Prompt: ${promptTokens}, Completion: ${completionTokens}, Total: ${totalTokens}`);
      }
    } else {
      // OpenRouter token usage
      const openRouterJson = json as OpenRouterResponse;
      promptTokens = openRouterJson.usage?.prompt_tokens || null;
      completionTokens = openRouterJson.usage?.completion_tokens || null;
      totalTokens = openRouterJson.usage?.total_tokens || null;
    }

    // Update batch status to COMPLETED and store token usage information
    await prisma.generationBatch.update({
      where: { id: batchId },
      data: {
        status: BatchStatus.COMPLETED,
        completedAt: new Date(),
        promptTokens: promptTokens,
        completionTokens: completionTokens,
        totalTokens: totalTokens
      }
    });

    // Log token usage information
    if (promptTokens && completionTokens && totalTokens) {
      console.log(`Token usage for batch ${batchId}:
        Prompt tokens: ${promptTokens}
        Completion tokens: ${completionTokens}
        Total tokens: ${totalTokens}`);
    }

    console.log(`Batch ${batchId} completed successfully`);
  } catch (error) {
    console.error(`Error processing batch ${batchId}: ${error}`);

    // Update batch status to FAILED
    await prisma.generationBatch.update({
      where: { id: batchId },
      data: {
        status: BatchStatus.FAILED
      }
    });
  }
}

/**
 * Main function to process all queue files
 */
async function processQueue(): Promise<void> {
  console.log('Starting question generation worker...');

  try {
    // Create the queue directory if it doesn't exist
    await fs.mkdir(queueDir, { recursive: true });

    // Read all files in the queue directory
    const files = await fs.readdir(queueDir);

    // Filter for generation batch files
    const batchFiles = files.filter(file => file.startsWith('generation-'));

    if (batchFiles.length === 0) {
      console.log('No batches to process');
      return;
    }

    console.log(`Found ${batchFiles.length} batches to process`);

    // Process each batch file
    for (const file of batchFiles) {
      try {
        // Read the file
        const filePath = path.join(queueDir, file);
        const content = await fs.readFile(filePath, 'utf-8');
        const { batchId, provider, language, tpDistribution } = JSON.parse(content) as QueueFile;

        // Process the batch
        await processBatch(batchId, provider, language as Language, tpDistribution);

        // Delete the file
        await fs.unlink(filePath);
      } catch (error) {
        console.error(`Error processing file ${file}: ${error}`);
      }
    }
  } catch (error) {
    console.error(`Error processing queue: ${error}`);
  }

  console.log('Question generation worker completed');
}

// Run the worker
processQueue()
  .catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

async function logGenerationData(batchId: number, prompt: string, response: any) {
  try {
    // Use an absolute path to ensure the logs directory is created in the right place
    const logsDir = path.resolve(__dirname, '..', 'logs', 'generation');
    
    // Ensure logs directory exists
    await fs.mkdir(logsDir, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const logFile = path.join(logsDir, `batch-${batchId}-${timestamp}.json`);
    
    await fs.writeFile(
      logFile,
      JSON.stringify({
        batchId,
        timestamp: new Date().toISOString(),
        prompt,
        response,
      }, null, 2),
      'utf-8'
    );
    
    console.log(`Generation data logged to ${logFile}`);
  } catch (error) {
    console.error(`Failed to log generation data: ${error}`);
  }
}
