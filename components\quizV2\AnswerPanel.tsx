import React from 'react';
import { useQuizV2 } from './QuizV2Provider';

export default function AnswerPanel() {
  const {
    questions,
    currentIndex
  } = useQuizV2();

  // Get the current question
  const currentQuestion = questions[currentIndex];

  // Since we're using the renderers in QuestionPanel, this component can be empty
  // The renderers handle the answer UI directly

  // We'll keep this component for future enhancements like:
  // - Showing additional feedback after submission
  // - Displaying hints
  // - AI tutor integration

  if (!currentQuestion) {
    return null;
  }

  return (
    <div className="p-5">
      {/* This space is reserved for future enhancements */}
    </div>
  );
}
