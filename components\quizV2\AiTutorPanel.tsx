import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useQuizV2 } from './QuizV2Provider';
import { v4 as uuidv4 } from 'uuid';
import AiTutorAvatar from './AiTutorAvatar';
import { getFromLocalStorage, setInLocalStorage, removeFromLocalStorage } from '../../hooks/useLocalStorage';

interface ChatMessage {
  id: string;
  sender: 'ai' | 'student';
  content: string;
  timestamp: Date;
}

interface AiTutorPanelProps {
  onCollapse?: () => void;
  onNewMessage?: (count: number) => void;
  onAddTranslation?: (callback: (translatedText: string, originalText: string) => void) => void;
  onAiMessage?: (message: string) => void; // New prop to notify of AI messages
}

export default function AiTutorPanel({ onCollapse, onNewMessage, onAddTranslation, onAiMessage }: AiTutorPanelProps) {
  const {
    questions,
    currentIndex,
    displayLanguage,
    allowAiTutor
  } = useQuizV2();

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentQuestion = questions[currentIndex];

  // Track if welcome message has been shown (used for UI state)
  const [welcomeMessageShown, setWelcomeMessageShown] = useState(false);

  // Load saved messages from localStorage on mount
  useEffect(() => {
    // Skip if no current question or not in browser
    if (!currentQuestion?.id || typeof window === 'undefined') return;

    // Check if welcome message has been shown for this question
    const welcomeShownKey = `welcome_shown_${currentQuestion.id}`;
    const hasWelcomeBeenShown = getFromLocalStorage(welcomeShownKey, false);

    console.log('🔍 Checking if welcome has been shown for question', currentQuestion.id, ':', hasWelcomeBeenShown);

    try {
      const savedMessagesData = getFromLocalStorage('ai_tutor_messages', null);
      if (savedMessagesData) {
        // Check if the saved messages are for the current question
        if (savedMessagesData.questionId === currentQuestion.id &&
            Array.isArray(savedMessagesData.messages) &&
            savedMessagesData.messages.length > 0) {

          console.log('🔄 Loaded AI tutor messages from localStorage:', savedMessagesData.messages.length, 'for question ID:', savedMessagesData.questionId);

          // Convert string timestamps back to Date objects
          const messagesWithDateObjects = savedMessagesData.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));

          setMessages(messagesWithDateObjects);

          // Mark welcome as shown since we have messages for this question
          if (!hasWelcomeBeenShown) {
            setInLocalStorage(welcomeShownKey, true);
          }

          setWelcomeMessageShown(true);
          return; // Skip adding welcome message if we loaded messages
        } else {
          console.log('💬 Saved messages are for a different question or empty. Current:', currentQuestion.id, 'Saved:', savedMessagesData.questionId);
        }
      }
    } catch (error) {
      console.error('Error loading AI tutor messages from localStorage:', error);
    }

    // If no saved messages for this question or error occurred
    if (!hasWelcomeBeenShown) {
      // Add welcome message only if it hasn't been shown before for this question
      const welcomeMessage: ChatMessage = {
        id: uuidv4(),
        sender: 'ai',
        content: displayLanguage === 'en'
          ? 'Hello! I\'m your AI tutor. How can I help you with this question?'
          : displayLanguage === 'ms'
            ? 'Hai! Saya adalah tutor AI anda. Bagaimana saya boleh membantu anda dengan soalan ini?'
            : '你好！我是你的AI导师。我能如何帮助你解答这个问题？',
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);

      // Store the welcome message with question ID
      const messageData = {
        questionId: currentQuestion.id,
        messages: [welcomeMessage]
      };

      // Save to localStorage
      setInLocalStorage('ai_tutor_messages', messageData);
      setInLocalStorage(welcomeShownKey, true); // Mark welcome as shown for this question
      console.log('💾 Saved welcome message for question ID:', currentQuestion.id);

      // Notify of new message
      if (onNewMessage) {
        onNewMessage(1);
      }

      setWelcomeMessageShown(true);
    } else {
      // Welcome has been shown before, but we don't have messages
      // This can happen if the messages were cleared but the welcome flag remains
      console.log('🔍 Welcome already shown for question', currentQuestion.id, 'but no messages found');

      // Initialize with empty messages array
      setMessages([]);
    }
  }, [currentQuestion?.id, displayLanguage, onNewMessage]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Helper function to clear welcome flags for old questions
  useEffect(() => {
    // Skip if not in browser
    if (typeof window === 'undefined') return;

    // This function cleans up old welcome flags to prevent localStorage from getting too full
    const cleanupWelcomeFlags = () => {
      try {
        // Get all localStorage keys
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('welcome_shown_')) {
            keys.push(key);
          }
        }

        // If we have more than 10 welcome flags, remove the oldest ones
        // This is a simple cleanup to prevent localStorage from getting too full
        if (keys.length > 10) {
          console.log('🧹 Cleaning up old welcome flags, found:', keys.length);

          // Sort by question ID (which is at the end of the key)
          keys.sort((a, b) => {
            const idA = parseInt(a.split('_').pop() || '0', 10);
            const idB = parseInt(b.split('_').pop() || '0', 10);
            return idA - idB;
          });

          // Remove the oldest ones, keeping the 5 most recent
          const keysToRemove = keys.slice(0, keys.length - 5);
          keysToRemove.forEach(key => {
            removeFromLocalStorage(key);
            console.log('🗑️ Removed old welcome flag:', key);
          });
        }
      } catch (error) {
        console.error('Error cleaning up welcome flags:', error);
      }
    };

    // Run the cleanup when the component mounts
    cleanupWelcomeFlags();
  }, []);

  // If AI tutor is not allowed, don't render the component
  if (!allowAiTutor) {
    return null;
  }

  // Add a message to the chat
  const addMessage = (sender: 'ai' | 'student', content: string) => {
    const newMessage: ChatMessage = {
      id: uuidv4(),
      sender,
      content,
      timestamp: new Date()
    };

    // Update messages state and store in localStorage for persistence
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);

    // Store messages with question ID to ensure they're loaded for the correct question
    const messageData = {
      questionId: currentQuestion?.id || 0,
      messages: updatedMessages
    };

    // Save to localStorage (safely)
    setInLocalStorage('ai_tutor_messages', messageData);
    console.log('💾 Saved AI tutor messages to localStorage:', updatedMessages.length, 'for question ID:', currentQuestion?.id);

    // Only increment unread count for AI messages when the panel is not visible
    // This prevents incrementing the count when the user is actively chatting
    if (sender === 'ai') {
      // Notify of new AI message for notification display
      if (onAiMessage && isCollapsed) {
        onAiMessage(content);
      }

      // Increment unread count
      if (onNewMessage && isCollapsed) {
        onNewMessage(1); // Increment unread count by 1
      }
    }
  };

  // Method to add a translation message
  const addTranslationMessage = useCallback((translatedText: string, originalText: string) => {
    // Create translation message
    const translationPrefix = displayLanguage === 'en'
      ? 'Translation of "'
      : displayLanguage === 'ms'
        ? 'Terjemahan untuk "'
        : '翻译 "';

    const translationSuffix = displayLanguage === 'en'
      ? '": '
      : displayLanguage === 'ms'
        ? '": '
        : '": ';

    const message = `${translationPrefix}${originalText}${translationSuffix}${translatedText}`;

    console.log('📝 Adding translation message to AI tutor chat:', message);

    // Create a new message object
    const newMessage: ChatMessage = {
      id: uuidv4(),
      sender: 'ai',
      content: message,
      timestamp: new Date()
    };

    // Update messages state directly to ensure it persists
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);

    // Store messages with question ID to ensure they're loaded for the correct question
    const messageData = {
      questionId: currentQuestion?.id || 0,
      messages: updatedMessages
    };

    // Save to localStorage (safely)
    setInLocalStorage('ai_tutor_messages', messageData);
    console.log('💾 Saved translation message to localStorage, total messages:', updatedMessages.length, 'for question ID:', currentQuestion?.id);

    // Don't expand the panel when a translation is added
    // Just increment the unread count which is handled in the addMessage function
  }, [displayLanguage, isCollapsed, onCollapse, messages]);

  // Expose the addTranslation method through the onAddTranslation prop
  useEffect(() => {
    if (onAddTranslation) {
      onAddTranslation(addTranslationMessage);
    }
  }, [onAddTranslation, addTranslationMessage]);

  // Handle asking the AI tutor
  const handleAskAiTutor = async () => {
    if (!input.trim() || !currentQuestion || isLoading) return;

    // Store the current input and clear the input field
    const question = input.trim();
    setInput('');

    // Add the student's message to the chat
    addMessage('student', question);

    // Add a "thinking" message from the AI
    const thinkingMessage = displayLanguage === 'en'
      ? 'Thinking...'
      : displayLanguage === 'ms'
        ? 'Berfikir...'
        : '思考中...';
    addMessage('ai', thinkingMessage);

    setIsLoading(true);

    try {
      // Prepare the question object with the necessary properties
      const questionData = {
        id: currentQuestion.id,
        promptEn: currentQuestion.promptEn || '',
        promptZh: currentQuestion.promptZh || '',
        promptMs: currentQuestion.promptMs || '',
        topic: currentQuestion.topic || '',
        type: currentQuestion.type || ''
      };

      const response = await fetch('/api/ai-tutor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: questionData,
          userQuestion: question,
          language: displayLanguage,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error getting AI tutor response: ${response.statusText}`);
      }

      const data = await response.json();

      // Remove the "thinking" message
      setMessages(prev => prev.filter(msg => msg.content !== thinkingMessage));

      // Add the AI's response
      addMessage('ai', data.explanation || 'I\'m not sure how to help with that.');
    } catch (error) {
      console.error('AI Tutor API error:', error);

      // Remove the "thinking" message
      setMessages(prev => prev.filter(msg => msg.content !== thinkingMessage));

      // Add an error message
      const errorMessage = displayLanguage === 'en'
        ? `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`
        : displayLanguage === 'ms'
          ? `Maaf, saya menghadapi ralat: ${error instanceof Error ? error.message : 'Ralat tidak diketahui'}`
          : `抱歉，我遇到了一个错误: ${error instanceof Error ? error.message : '未知错误'}`;
      addMessage('ai', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle key press in the input field
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && input.trim()) {
      e.preventDefault();
      handleAskAiTutor();
    }
  };

  // Render a chat bubble
  const renderChatBubble = (message: ChatMessage) => {
    const isAi = message.sender === 'ai';

    return (
      <div
        key={message.id}
        className={`flex ${isAi ? 'justify-start' : 'justify-end'} mb-3 items-end`}
      >
        {isAi && (
          <div className="mr-2 flex-shrink-0 relative">
            <AiTutorAvatar size="sm" />
            {/* Only show notification badge on the first message and only if it's the latest message */}
            {message.id === messages[0]?.id && messages.length === 1 && (
              <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                1
              </div>
            )}
          </div>
        )}
        <div
          className={`max-w-[80%] rounded-lg px-4 py-2 ${
            isAi
              ? 'bg-white text-gray-800 rounded-bl-none shadow-md border border-gray-200'
              : 'bg-green-500 text-white rounded-tr-none shadow-md'
          }`}
          style={isAi ? { position: 'relative', marginLeft: '0' } : {}}
        >
          {/* Add a small triangle for AI messages */}
          {isAi && (
            <div
              className="absolute w-3 h-3 bg-white border-l border-b border-gray-200 transform rotate-45"
              style={{ left: '-6px', bottom: '10px' }}
            ></div>
          )}
          {isAi && (
            <div className="font-bold mb-1">
              {displayLanguage === 'en' ? 'AI Tutor' :
               displayLanguage === 'ms' ? 'Tutor AI' : 'AI 导师'}
            </div>
          )}
          <div className="prose prose-sm max-w-none">
            {message.content}
          </div>
          <div className={`text-xs mt-1 text-right ${isAi ? 'text-gray-500' : 'text-green-200'}`}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    );
  };

  // If the panel is collapsed, call the onCollapse callback
  useEffect(() => {
    if (isCollapsed && onCollapse) {
      onCollapse();
    }
  }, [isCollapsed, onCollapse]);

  return (
    <div className="bg-blue-100 rounded-lg p-4 flex flex-col h-full max-h-[600px] md:max-h-[700px]">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-lg">
          {displayLanguage === 'en' ? 'AI Tutor' :
           displayLanguage === 'ms' ? 'Tutor AI' : 'AI 导师'}
        </h3>
        <button
          onClick={() => setIsCollapsed(true)}
          className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200 transition-colors"
          aria-label="Hide AI Tutor"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Chat history */}
      <div className="flex-grow overflow-y-auto mb-4 p-4 md:p-4 sm:p-3 max-h-[400px] md:max-h-[500px]">
        {messages.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center text-gray-500 italic">
              {displayLanguage === 'en'
                ? 'Ask me anything about the question!'
                : displayLanguage === 'ms'
                  ? 'Tanya saya apa-apa tentang soalan ini!'
                  : '问我任何关于问题的事情！'}
            </div>
          </div>
        ) : (
          <>
            {messages.map(renderChatBubble)}
            <div ref={messagesEndRef} />

            {/* Debug info - only visible in development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-gray-400 mt-4 border-t pt-2">
                <p>Total messages: {messages.length}</p>
                <p>Translation messages: {messages.filter(msg =>
                  msg.content.includes('Translation of "') ||
                  msg.content.includes('翻译 "') ||
                  msg.content.includes('Terjemahan untuk "')
                ).length}</p>
                <p>Question ID: {currentQuestion?.id}</p>
                <p>Panel state: {isCollapsed ? 'Collapsed' : 'Expanded'}</p>
                <button
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      localStorage.removeItem('ai_tutor_messages');
                      localStorage.removeItem(`welcome_shown_${currentQuestion?.id}`);
                      console.log('🧹 Cleared AI tutor messages and welcome flag');
                      setMessages([]);
                      setWelcomeMessageShown(false);
                    }
                  }}
                  className="text-red-500 hover:text-red-700 mt-1"
                >
                  Clear Messages
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Chat input */}
      <div className="flex">
        <textarea
          value={input}
          onChange={e => setInput(e.target.value)}
          placeholder={displayLanguage === 'en' ? 'Ask the AI Tutor...' :
                       displayLanguage === 'ms' ? 'Tanya Tutor AI...' : '问AI导师…'}
          className="w-full text-black rounded-l p-3 resize-none"
          rows={2}
          onKeyDown={handleKeyDown}
          disabled={isLoading}
        />
        <button
          onClick={handleAskAiTutor}
          className={`${isLoading ? 'bg-gray-500' : 'bg-blue-500 hover:bg-blue-600'} text-white font-semibold py-2 px-4 md:px-6 rounded-r focus:outline-none flex items-center justify-center min-w-[60px]`}
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="animate-pulse">...</span>
          ) : (
            <>
              {displayLanguage === 'en' ? 'Send' :
               displayLanguage === 'ms' ? 'Hantar' : '发送'}
            </>
          )}
        </button>
      </div>
    </div>
  );
}
