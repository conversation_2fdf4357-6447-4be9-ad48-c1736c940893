import React from 'react';
import { useRouter } from 'next/router';
import { useQuiz } from './QuizContext';

interface QuizHeaderProps {
  // Props are optional since we can use context
  total?: number;
  index?: number;
  displayLanguage?: 'en' | 'zh' | 'ms';
  onToggleLang?: () => void;
  onToggleKeywords?: () => void;
  onBreak?: () => void;
  subject?: string;
  unit?: number | null;
  topic?: string | null;
}

const QuizHeader: React.FC<QuizHeaderProps> = (props) => {
  const router = useRouter();
  const {
    questions,
    currentIndex,
    displayLanguage,
    toggleLanguage,
    toggleKeywords,
    showKeywords,
    quizAttemptId
  } = useQuiz();

  // Use props if provided, otherwise use context
  const total = props.total ?? questions.length;
  const index = props.index ?? currentIndex;
  const lang = props.displayLanguage ?? displayLanguage;
  const currentQuestion = questions[currentIndex];

  const subject = props.subject ?? currentQuestion?.subject?.name ?? 'N/A';
  const unit = props.unit ?? currentQuestion?.unit?.unitNumber ?? null;
  const topic = props.topic ?? currentQuestion?.unit?.topicEn ?? currentQuestion?.topic ?? null;

  const handleBreak = async () => {
    // If onBreak prop is provided, use it
    if (props.onBreak) {
      props.onBreak();
      return;
    }

    // Otherwise use the default implementation
    if (quizAttemptId !== null) {
      try {
        await fetch('/api/log-quiz-attempt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            quizAttemptId: quizAttemptId,
            currentQuestionIndex: currentIndex,
          }),
        });
        // Navigate to student dashboard after saving
        router.push('/student-dashboard');
      } catch (error) {
        console.error('Error saving quiz progress:', error);
        // Navigate anyway even if there's an error
        router.push('/student-dashboard');
      }
    } else {
      // If no quizAttemptId, just navigate
      router.push('/student-dashboard');
    }
  };

  // Format the question type for display
  const formatQuestionType = (type: string): string => {
    if (!type) return '';

    // Replace underscores with spaces and capitalize each word
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  // Get the current question type
  const questionType = currentQuestion?.type?.toString() || '';
  const formattedType = formatQuestionType(questionType);

  // Calculate progress percentage
  const progressPercentage = Math.round(((index + 1) / total) * 100);

  return (
    <>
      <header className="bg-white shadow-sm sticky top-0 z-10">
        {/* Progress bar */}
        <div className="w-full bg-[#E6F0FA] h-1 rounded-full shadow-sm">
          <div 
            className="bg-[#0F5FA6] h-1 rounded-full transition-all duration-300 ease-in-out" 
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        
        {/* Progress text */}
        <div className="text-sm text-gray-600 text-center py-1 bg-gray-50">
          {lang === 'en' 
            ? `Question ${index + 1} of ${total}` 
            : lang === 'ms' 
              ? `Soalan ${index + 1} daripada ${total}`
              : `问题 ${index + 1} / ${total}`}
        </div>

        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center text-sm text-gray-700">
              {subject || 'N/A'} - Unit {unit || 'N/A'} - {topic || 'N/A'}
              {formattedType && (
                <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
                  {formattedType}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBreak}
                className="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600 transition-colors flex items-center"
                title={lang === 'en' ? 'Take a Break' : lang === 'zh' ? '休息一下' : 'Rehat Sebentar'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {lang === 'en' ? 'Break' : lang === 'zh' ? '休息' : 'Rehat'}
              </button>
              <button
                onClick={props.onToggleLang ?? toggleLanguage}
                className="bg-white text-blue-600 px-3 py-1 rounded text-sm hover:bg-blue-50 transition-colors border border-blue-100"
                title={lang === 'en' ? 'Switch to Chinese' : lang === 'zh' ? '切换至英文' : 'Tukar ke Bahasa Inggeris'}
              >
                {lang === 'en' ? '中文' : lang === 'zh' ? 'EN' : 'English'}
              </button>
              <button
                onClick={props.onToggleKeywords ?? toggleKeywords}
                className={`${showKeywords ? 'bg-green-500 text-white' : 'bg-white text-green-600'} px-3 py-1 rounded text-sm hover:bg-green-50 transition-colors flex items-center border ${showKeywords ? 'border-green-500' : 'border-green-100'}`}
                title={lang === 'en' ? 'Highlight Keywords' : lang === 'zh' ? '突出关键词' : 'Sorot Kata Kunci'}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                {lang === 'en' ? 'Hint' : lang === 'zh' ? '提示' : 'Petunjuk'}
              </button>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default QuizHeader;
