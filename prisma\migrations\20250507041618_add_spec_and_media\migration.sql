-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "QuestionType" ADD VALUE 'MULTIPLE_CHOICE_IMAGE';
ALTER TYPE "QuestionType" ADD VALUE 'PICTURE_PROMPT';
ALTER TYPE "QuestionType" ADD VALUE 'FILL_IN_THE_BLANK';
ALTER TYPE "QuestionType" ADD VALUE 'TRUE_FALSE';
ALTER TYPE "QuestionType" ADD VALUE 'LONG_ANSWER';
ALTER TYPE "QuestionType" ADD VALUE 'MATCHING';
ALTER TYPE "QuestionType" ADD VALUE 'SEQUENCING';

-- AlterTable
ALTER TABLE "TG_Choice" ADD COLUMN     "mediaId" INTEGER;

-- AlterTable
ALTER TABLE "TG_Question" ADD COLUMN     "promptMediaId" INTEGER,
ADD COLUMN     "spec" JSONB;

-- CreateTable
CREATE TABLE "TG_Media" (
    "id" SERIAL NOT NULL,
    "url" TEXT NOT NULL,
    "altEn" TEXT,
    "altZh" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TG_Media_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "TG_Question" ADD CONSTRAINT "TG_Question_promptMediaId_fkey" FOREIGN KEY ("promptMediaId") REFERENCES "TG_Media"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Choice" ADD CONSTRAINT "TG_Choice_mediaId_fkey" FOREIGN KEY ("mediaId") REFERENCES "TG_Media"("id") ON DELETE SET NULL ON UPDATE CASCADE;
