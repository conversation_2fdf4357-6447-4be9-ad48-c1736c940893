import { QuestionType } from '@prisma/client';
import { QuestionSpec } from './question';

export interface Choice {
  key: string;
  textZh: string;
  textEn: string;
  textMs?: string;
  mediaUrl?: string;
  mediaAlt?: string;
}

export interface Media {
  id: number;
  url: string;
  altEn?: string;
  altZh?: string;
  altMs?: string;
}

export interface Keywords {
  en?: string[];
  zh?: string[];
  ms?: string[];
}

export interface Question {
  id: number;
  questionId: string;
  type: QuestionType;
  promptEn: string;
  promptZh: string;
  promptMs?: string;
  promptMedia?: Media;
  spec?: any; // Will be cast to the appropriate type based on question type
  keywords?: Keywords; // Highlightable keywords for each language
  originalLanguage?: 'EN' | 'ZH' | 'MS';
  translationState?: 'NONE' | 'PARTIAL' | 'COMPLETE';
  choices?: Choice[];
  answer: string | { zh: string; en: string };
  explanation: { zh: string; en: string };
  topic: string;
  subject: { name: string };
  unit: { unitNumber: number; topicEn: string; topicZh: string };
}

export interface QuizAttempt {
  id: number;
  childId: number;
  subjectId: number;
  unitId?: number;
  questionIds: string[];
  currentQuestionIndex: number;
  score?: number;
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELED';
  quizType: 'MASTERY' | 'TEST' | 'QUICK';
  createdAt: string;
  updatedAt: string;
}

export interface QuizResult {
  questionId: number;
  chosenAnswer: string;
  isCorrect: boolean | null;
  timeSpentSeconds: number | null;
}
