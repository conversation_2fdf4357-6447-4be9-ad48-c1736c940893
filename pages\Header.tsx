import React from 'react';
import { GemIcon, HeartIcon } from 'lucide-react';
interface HeaderProps {
  stats: {
    streak: number;
    xp: number;
    gems: number;
    hearts: number;
  };
  onStreakClick: () => void;
}
export const Header: React.FC<HeaderProps> = ({
  stats,
  onStreakClick
}) => {
  return <header className="sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md">
      <div className="text-xl font-bold">LearnQuest</div>
      <div className="flex items-center space-x-4">
        <button onClick={onStreakClick} className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1">
          <div size={20} className="text-[#05DBF2] mr-1" />
          <span className="font-bold">{stats.streak}</span>
        </button>
        <div className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1">
          <GemIcon size={20} className="text-[#05DBF2] mr-1" />
          <span className="font-bold">{stats.gems}</span>
        </div>
        <div className="flex items-center bg-[#0A8CBF] rounded-full px-3 py-1">
          <HeartIcon size={20} className="text-red-500 mr-1" />
          <span className="font-bold">{stats.hearts}</span>
        </div>
      </div>
    </header>;
};