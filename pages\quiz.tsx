import { NextPage } from 'next';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import QuizShell from '../components/quiz/QuizShell';

const QuizPage: NextPage = () => {
  const router = useRouter();
  const { attemptId } = router.query;

  // This page is now a simple wrapper that redirects to the quiz/[attemptId] route
  // if an attemptId is provided in the query parameters

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (attemptId) {
        // Check if we should use v2 quiz
        const quizVersion = process.env.NEXT_PUBLIC_QUIZ_VERSION || 'v1';
        if (quizVersion === 'v2') {
          // If v2, redirect to the v2 quiz route
          router.replace(`/quiz/v2/${attemptId}`);
        } else {
          // Otherwise, use the v1 route
          router.replace(`/quiz/${attemptId}`);
        }
      } else {
        // If no attemptId, redirect to student dashboard
        router.replace('/student-dashboard');
      }
    }
  }, [attemptId, router]);

  // Show loading state while redirecting
  return (
    <>
      <Head>
        <title>Quiz | My Quiz App</title>
        <meta name="description" content="Loading Quiz..." />
      </Head>
      <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full text-center">
          <p className="text-xl mb-4">Loading Quiz...</p>
          <div className="loader mx-auto mb-4 w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    </>
  );
};

export default QuizPage;
