import * as argon2 from 'argon2';
import crypto from 'crypto';

export async function hashPassword(password: string): Promise<{ hash: string; salt: string }> {
  const salt = crypto.randomBytes(16).toString('base64');
  const hash = await argon2.hash(password + salt, {
    type: argon2.argon2id,
    memoryCost: 65536, // 64MB in KB
    timeCost: 3,
    parallelism: 4,
  });
  return { hash, salt };
}

export async function verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
  return await argon2.verify(hash, password + salt);
}

export async function hashPin(pin: string, salt?: string): Promise<{ hash: string; salt: string }> {
  // Reuse parent's salt if provided, otherwise generate new one
  const pinSalt = salt || crypto.randomBytes(16).toString('base64');
  const hash = await argon2.hash(pin + pinSalt, {
    type: argon2.argon2id,
    memoryCost: 65536,
    timeCost: 3,
    parallelism: 4,
  });
  return { hash, salt: pinSalt };
}