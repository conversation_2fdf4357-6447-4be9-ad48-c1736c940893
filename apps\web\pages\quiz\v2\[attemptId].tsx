import { GetServerSideProps, NextPage } from 'next';
import Head from 'next/head';
import { getSession } from 'next-auth/react';
import {
  QuizV2Layout,
  QuizV2Provider,
  ProgressBar,
  QuestionPanel,
  AnswerPanel,
  NextButtonBar,
  LanguageToggle,
  Question<PERSON>ounter,
  HintFab
} from '../../../components/quizV2';

interface QuizV2PageProps {
  attempt: any; // Will be properly typed later
}

const QuizV2Page: NextPage<QuizV2PageProps> = ({ attempt }) => {
  return (
    <>
      <Head>
        <title>Quiz V2 | My Quiz App</title>
        <meta name="description" content="Take a quiz with our new interface" />
      </Head>

      <QuizV2Provider attempt={attempt}>
        <QuizV2Layout>
          <div className="flex items-center p-4 border-b-2 border-gray-200">
            <div className="w-1/4">
              <QuestionCounter />
            </div>
            <div className="w-1/2">
              <ProgressBar />
            </div>
            <div className="w-1/4 flex justify-end space-x-2">
              <HintFab inline={true} />
              <LanguageToggle />
            </div>
          </div>
          <QuestionPanel />
          <AnswerPanel />
          <NextButtonBar />
        </QuizV2Layout>
      </QuizV2Provider>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getSession(context);

  // Redirect to login if not authenticated
  if (!session) {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  const { attemptId } = context.params || {};

  // Fetch quiz attempt data with choices, answer, and spec included
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/quiz/${attemptId}?include=choices,answer,spec`,
      {
        headers: {
          cookie: context.req.headers.cookie || '',
        },
      }
    );

    if (!res.ok) {
      const errorData = await res.json();
      if (res.status === 403) {
        return {
          redirect: {
            destination: '/student-dashboard?error=unauthorized-quiz-access',
            permanent: false,
          },
        };
      }
      throw new Error(errorData.message || `Failed to fetch quiz attempt: ${res.status}`);
    }

    const attempt = await res.json();

    // If the quiz is already completed, redirect to the dashboard
    if (attempt.isCompleted) {
      return {
        redirect: {
          destination: '/student-dashboard?message=quiz-already-completed',
          permanent: false,
        },
      };
    }

    return {
      props: {
        attempt,
      },
    };
  } catch (error) {
    console.error('Error fetching quiz attempt:', error);

    // For now, return placeholder data for development
    return {
      props: {
        attempt: {
          quizAttempt: {
            id: Number(attemptId),
            currentQuestionIndex: 0,
            status: 'ACTIVE',
            quizType: 'MASTERY',
          },
          questions: [
            {
              id: 1,
              promptEn: 'What is the main source of energy for Earth?',
              choices: [
                { id: 1, textEn: 'Wind' },
                { id: 2, textEn: 'Water' },
                { id: 3, textEn: 'Sun' },
                { id: 4, textEn: 'Coal' },
              ],
              correctAnswer: 'Sun',
              explanation: {
                textEn: 'The Sun is the main source of energy for Earth, providing light and heat that sustains life.'
              }
            },
            {
              id: 2,
              promptEn: 'Which planet is known as the Red Planet?',
              choices: [
                { id: 5, textEn: 'Venus' },
                { id: 6, textEn: 'Mars' },
                { id: 7, textEn: 'Jupiter' },
                { id: 8, textEn: 'Saturn' },
              ],
              correctAnswer: 'Mars',
              explanation: {
                textEn: 'Mars is known as the Red Planet due to its reddish appearance, which is caused by iron oxide (rust) on its surface.'
              }
            },
          ],
          isCompleted: false,
        },
      },
    };
  }
};

export default QuizV2Page;
