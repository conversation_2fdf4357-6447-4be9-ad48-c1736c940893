import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { getSubjects, getTopics, Subject, Unit } from '../lib/api';
import StepMode from './StepMode';
import StepSubject from './StepSubject';
import StepTopic from './StepTopic';
import StepConfirm from './StepConfirm';

export type QuizMode = 'mastery' | 'test' | 'quick';

export interface QuizFormState {
  mode: QuizMode | null;
  subjectId: number | null;
  unitId: number | null;
  autoSelectTopic: boolean;
}

const StartQuiz = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [step, setStep] = useState<1 | 2 | 3 | 4>(1);
  const [form, setForm] = useState<QuizFormState>({
    mode: null,
    subjectId: null,
    unitId: null,
    autoSelectTopic: false,
  });
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [topics, setTopics] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Fetch subjects on component mount
  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        setLoading(true);
        const data = await getSubjects();
        setSubjects(data);
      } catch (err) {
        console.error('Error fetching subjects:', err);
        setError('Failed to load subjects. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (status === 'authenticated') {
      fetchSubjects();
    }
  }, [status]);

  // Fetch topics when subject changes
  useEffect(() => {
    const fetchTopics = async () => {
      if (form.subjectId) {
        try {
          setLoading(true);
          const data = await getTopics(form.subjectId);
          setTopics(data);
        } catch (err) {
          console.error(`Error fetching topics for subject ${form.subjectId}:`, err);
          setError('Failed to load topics. Please try again.');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchTopics();
  }, [form.subjectId]);

  // Handle mode selection
  const handleModeSelect = (mode: QuizMode) => {
    console.log('StartQuiz step', step, 'mode selected:', mode);
    setForm({ ...form, mode });

    if (mode === 'quick') {
      // For Quick Quiz, skip to creating the quiz attempt immediately
      handleCreateQuickQuiz();
    } else {
      // For other modes, advance to subject selection
      setStep(2);
    }
  };

  // Handle subject selection
  const handleSubjectSelect = (subjectId: number) => {
    console.log('StartQuiz step', step, 'subject selected:', subjectId);
    setForm({ ...form, subjectId });
    setStep(3);
  };

  // Handle topic selection method
  const handleTopicSelectionMethod = (autoSelect: boolean, unitId: number | null = null) => {
    console.log('StartQuiz step', step, 'topic selection method:', autoSelect ? 'auto' : 'manual', unitId);
    setForm({ ...form, autoSelectTopic: autoSelect, unitId });
    setStep(4);
  };

  // Handle quiz creation
  const handleCreateQuiz = async () => {
    console.log('StartQuiz step', step, 'creating quiz with form:', form);
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/quiz-attempts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          mode: form.mode,
          subjectId: form.subjectId,
          unitId: form.unitId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error creating quiz: ${response.statusText}`);
      }

      const data = await response.json();

      // Use the quiz version from environment variable if available
      const quizVersion = process.env.NEXT_PUBLIC_QUIZ_VERSION || 'v1';
      if (quizVersion === 'v2') {
        router.push(`/quiz/v2/${data.id}`);
      } else {
        router.push(`/quiz/${data.id}`);
      }
    } catch (err) {
      console.error('Error creating quiz:', err);

      // Try to get more detailed error information
      let errorMessage = 'Failed to create quiz. Please try again.';

      if (err instanceof Error) {
        errorMessage = `Error: ${err.message}`;
      }

      // For now, just use the error message
      // We can't access the response object here after the catch

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle quick quiz creation
  const handleCreateQuickQuiz = async () => {
    console.log('StartQuiz creating quick quiz');
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/quiz-attempts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          mode: 'quick',
          subjectId: null,
          unitId: null,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error creating quick quiz: ${response.statusText}`);
      }

      const data = await response.json();

      // Use the quiz version from environment variable if available
      const quizVersion = process.env.NEXT_PUBLIC_QUIZ_VERSION || 'v1';
      if (quizVersion === 'v2') {
        router.push(`/quiz/v2/${data.id}`);
      } else {
        router.push(`/quiz/${data.id}`);
      }
    } catch (err) {
      console.error('Error creating quick quiz:', err);

      // Try to get more detailed error information
      let errorMessage = 'Failed to create quick quiz. Please try again.';

      if (err instanceof Error) {
        errorMessage = `Error: ${err.message}`;
      }

      // For now, just use the error message
      // We can't access the response object here after the catch

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // If not authenticated, show loading state
  if (status === 'loading' || status === 'unauthenticated') {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Start a New Quiz</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <>
          {step === 1 && <StepMode onSelect={handleModeSelect} />}

          {step === 2 && (
            <StepSubject
              subjects={subjects}
              onSelect={handleSubjectSelect}
              onBack={() => setStep(1)}
            />
          )}

          {step === 3 && (
            <StepTopic
              topics={topics}
              onSelect={handleTopicSelectionMethod}
              onBack={() => setStep(2)}
            />
          )}

          {step === 4 && (
            <StepConfirm
              formData={form}
              subjectName={subjects.find(s => s.id === form.subjectId)?.name || ''}
              topicName={topics.find(t => t.id === form.unitId)?.topicEn || 'Auto-selected'}
              onConfirm={handleCreateQuiz}
              onBack={() => setStep(3)}
            />
          )}
        </>
      )}
    </div>
  );
};

export default StartQuiz;
