import { PrismaClient } from '@prisma/client';

/**
 * This script deletes questions with null generationBatchId.
 * 
 * IMPORTANT: Question Deletion Order
 * When deleting questions, you must delete related records in the following order
 * due to foreign key constraints:
 * 
 * 1. Choice - Multiple choice options for questions
 * 2. Answer - Correct answers for questions
 * 3. ExplanationText - Explanations for questions
 * 4. TranslationLog - Logs of translation operations on questions
 * 5. StudentAnswer - Student responses to questions
 * 6. Question - The actual question records
 * 
 * If you modify the Question schema or add new relations, update this list
 * and the deletion process below.
 */

// Initialize Prisma client
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting deletion of questions with null generationBatchId...');
    
    // Get count of questions to be deleted
    const count = await prisma.question.count({
      where: {
        generationBatchId: null
      }
    });
    
    console.log(`Found ${count} questions with null generationBatchId`);
    
    // Get the IDs of questions to be deleted
    const questionsToDelete = await prisma.question.findMany({
      where: {
        generationBatchId: null
      },
      select: {
        id: true
      }
    });
    
    const questionIds = questionsToDelete.map(q => q.id);
    
    // First delete related choices
    const deletedChoices = await prisma.choice.deleteMany({
      where: {
        questionId: {
          in: questionIds
        }
      }
    });
    
    console.log(`Deleted ${deletedChoices.count} related choices`);
    
    // Delete related answers
    const deletedAnswers = await prisma.answer.deleteMany({
      where: {
        questionId: {
          in: questionIds
        }
      }
    });
    
    console.log(`Deleted ${deletedAnswers.count} related answers`);
    
    // Delete related explanation texts
    const deletedExplanations = await prisma.explanationText.deleteMany({
      where: {
        questionId: {
          in: questionIds
        }
      }
    });
    
    console.log(`Deleted ${deletedExplanations.count} related explanations`);
    
    // Delete related translation logs
    const deletedTranslationLogs = await prisma.translationLog.deleteMany({
      where: {
        questionId: {
          in: questionIds
        }
      }
    });
    
    console.log(`Deleted ${deletedTranslationLogs.count} related translation logs`);
    
    // Delete related student answers
    const deletedStudentAnswers = await prisma.studentAnswer.deleteMany({
      where: {
        questionId: {
          in: questionIds
        }
      }
    });
    
    console.log(`Deleted ${deletedStudentAnswers.count} related student answers`);
    
    // Now delete the questions
    const result = await prisma.question.deleteMany({
      where: {
        generationBatchId: null
      }
    });
    
    console.log(`Successfully deleted ${result.count} questions`);
  } catch (error) {
    console.error('Error deleting questions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  });





