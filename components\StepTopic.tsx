import React, { useState } from 'react';
import { Unit } from '../lib/api';

interface StepTopicProps {
  topics: Unit[];
  onSelect: (autoSelect: boolean, unitId: number | null) => void;
  onBack: () => void;
}

const StepTopic: React.FC<StepTopicProps> = ({ topics, onSelect, onBack }) => {
  const [selectionMethod, setSelectionMethod] = useState<'auto' | 'manual'>('auto');
  const [selectedTopicId, setSelectedTopicId] = useState<number | null>(null);

  const handleContinue = () => {
    if (selectionMethod === 'auto') {
      console.log('Topic selection: auto-select');
      onSelect(true, null);
    } else if (selectedTopicId) {
      console.log('Topic selection: manual, topic ID:', selectedTopicId);
      onSelect(false, selectedTopicId);
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Select Topic</h2>
      
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <input
            type="radio"
            id="auto-select"
            name="topic-selection"
            checked={selectionMethod === 'auto'}
            onChange={() => setSelectionMethod('auto')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="auto-select" className="text-gray-700">
            Auto-select topics (recommended)
          </label>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="radio"
            id="manual-select"
            name="topic-selection"
            checked={selectionMethod === 'manual'}
            onChange={() => setSelectionMethod('manual')}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="manual-select" className="text-gray-700">
            Select a specific topic
          </label>
        </div>
      </div>
      
      {selectionMethod === 'manual' && (
        <div className="mt-4">
          <label htmlFor="topic-select" className="block text-sm font-medium text-gray-700 mb-1">
            Choose a topic
          </label>
          <select
            id="topic-select"
            value={selectedTopicId || ''}
            onChange={(e) => setSelectedTopicId(Number(e.target.value))}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="">Select a topic</option>
            {topics.map((topic) => (
              <option key={topic.id} value={topic.id}>
                Unit {topic.unitNumber}: {topic.topicEn}
              </option>
            ))}
          </select>
        </div>
      )}
      
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Back
        </button>
        
        <button
          onClick={handleContinue}
          disabled={selectionMethod === 'manual' && !selectedTopicId}
          className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${
            selectionMethod === 'manual' && !selectedTopicId
              ? 'bg-blue-300 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
          }`}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default StepTopic;
