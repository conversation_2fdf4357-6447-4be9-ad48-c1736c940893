import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Fetch all generation batches with related data
    const batches = await prisma.generationBatch.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        admin: {
          select: {
            name: true,
            email: true
          }
        },
        year: {
          select: {
            yearNumber: true
          }
        },
        subject: {
          select: {
            name: true
          }
        },
        unit: {
          select: {
            unitNumber: true,
            topicEn: true
          }
        },
        questions: {
          select: {
            id: true
          }
        },
        notes: {
          select: {
            id: true
          }
        }
      }
    });

    // Transform the data to include counts
    const batchesWithCounts = batches.map(batch => ({
      id: batch.id,
      adminId: batch.adminId,
      adminName: batch.admin.name,
      yearId: batch.yearId,
      yearNumber: batch.year.yearNumber,
      subjectId: batch.subjectId,
      subjectName: batch.subject.name,
      unitId: batch.unitId,
      unitNumber: batch.unit?.unitNumber,
      unitTopic: batch.unit?.topicEn,
      questionTypes: batch.questionTypes,
      numQuestions: batch.numQuestions,
      modelUsed: batch.modelUsed,
      language: batch.language || 'ZH', // Include language, default to ZH for backward compatibility
      status: batch.status,
      jobId: batch.jobId,
      createdAt: batch.createdAt,
      completedAt: batch.completedAt,
      questionCount: batch.questions.length,
      noteCount: batch.notes.length,
      promptTokens: batch.promptTokens,
      completionTokens: batch.completionTokens,
      totalTokens: batch.totalTokens
    }));

    return res.status(200).json(batchesWithCounts);
  } catch (error) {
    console.error('Error fetching generation batches:', error);
    return res.status(500).json({ message: 'Internal server error', error: String(error) });
  }
}
