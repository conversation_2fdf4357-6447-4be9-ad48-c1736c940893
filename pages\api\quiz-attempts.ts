import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import { Prisma, QuizMode } from '@prisma/client';

interface RawQueryResult {
  id: number;
  questionId: string;
  type: string;
  promptEn: string;
  promptZh: string;
  topic: string;
  choice_id: number | null;
  choice_key: string | null;
  choice_en: string | null;
  choice_zh: string | null;
  answer_key: string | null;
  answer_en: string | null;
  answer_zh: string | null;
  explanation_en: string | null;
  explanation_zh: string | null;
  subject: string;
  unit: number;
  tp_level: number;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { mode, subjectId, unitId, childId, tpLevel, tpDistribution } = req.body;

    // Validate required fields
    if (!mode) {
      return res.status(400).json({ message: 'Quiz mode is required' });
    }

    // For Quick Quiz mode, we don't need subjectId or unitId
    if (mode !== 'quick' && !subjectId) {
      return res.status(400).json({ message: 'Subject ID is required for non-quick quizzes' });
    }

    // Validate tpLevel if provided
    if (tpLevel && (tpLevel < 1 || tpLevel > 6)) {
      return res.status(400).json({ message: 'TP level must be between 1 and 6' });
    }

    // Validate tpDistribution if provided
    if (tpDistribution && (!Array.isArray(tpDistribution) ||
        tpDistribution.some(level => level < 1 || level > 6))) {
      return res.status(400).json({ message: 'TP distribution must be an array of levels between 1 and 6' });
    }

    // Fetch the quiz configuration based on mode
    let configuredLimit = 5; // Default fallback
    let quizConfig = null;

    try {
      // Convert mode to QuizMode enum value
      const quizMode = mode.toUpperCase() as QuizMode;

      // Fetch the quiz config for this mode
      quizConfig = await prisma.quizConfig.findUnique({
        where: { mode: quizMode },
      });

      if (quizConfig) {
        configuredLimit = quizConfig.numQuestions;
        console.log(`Using quiz config for ${mode} mode: numQuestions=${configuredLimit}`);
      } else {
        // Fallback to the old setting if no config found
        const limitSetting = await prisma.setting.findUnique({ where: { key: 'quizQuestionLimit' } });
        configuredLimit = limitSetting?.value ? parseInt(limitSetting.value, 10) : 5;
        console.warn(`No quiz config found for ${mode} mode, using fallback limit: ${configuredLimit}`);
      }
    } catch (configError) {
      console.error('Error fetching quiz config:', configError);
      // Fallback to the old setting
      const limitSetting = await prisma.setting.findUnique({ where: { key: 'quizQuestionLimit' } });
      configuredLimit = limitSetting?.value ? parseInt(limitSetting.value, 10) : 5;
    }

    // Validate the configured limit
    if (isNaN(configuredLimit) || configuredLimit <= 0) {
      console.warn(`Invalid question limit: ${configuredLimit}. Defaulting to 5.`);
      configuredLimit = 5;
    }

    // Determine the child ID to use
    let effectiveChildId: number;

    if (childId) {
      effectiveChildId = Number(childId);
    } else {
      // Get the first child associated with the authenticated user
      // Check if session.user.parentId exists (for parent accounts)
      const parentId = session.user.parentId || session.user.id;

      // Find the account and its children
      const account = await prisma.account.findUnique({
        where: { id: Number(parentId) },
        include: { children: true },
      });

      if (!account || !account.children || account.children.length === 0) {
        // For testing purposes, create a dummy child if none exists
        const dummyChild = await prisma.child.create({
          data: {
            name: 'Test Child',
            year: 'Year 5',
            username: `test_${Date.now()}`,
            accountId: Number(parentId),
          },
        });
        effectiveChildId = dummyChild.id;
      } else {
        effectiveChildId = account.children[0].id;
      }
    }

    // Fetch questions based on mode
    let questions: RawQueryResult[] = [];
    let subjectRecord = null;
    let unitRecord = null;

    if (subjectId) {
      subjectRecord = await prisma.subject.findUnique({ where: { id: Number(subjectId) } });
      if (!subjectRecord) {
        return res.status(404).json({ message: 'Subject not found' });
      }
    }

    if (unitId) {
      unitRecord = await prisma.unit.findUnique({ where: { id: Number(unitId) } });
      if (!unitRecord) {
        return res.status(404).json({ message: 'Unit not found' });
      }
    }

    // For quick quiz, fetch random questions
    try {
      if (mode === 'quick') {
        // Build where clause for quick quiz
        let whereClause = Prisma.sql`q_sub."promptEn" IS NOT NULL`;

        // Add TP level filter if provided
        if (tpLevel) {
          whereClause = Prisma.sql`${whereClause} AND q_sub."tpLevel" = ${Number(tpLevel)}`;
        }

        try {
          questions = await prisma.$queryRaw<RawQueryResult[]>(
            Prisma.sql`
              SELECT
                q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic,
                c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
                a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
                e."textEn" as explanation_en, e."textZh" as explanation_zh,
                s.name as subject, u."unitNumber" as unit,
                q."tpLevel" as tp_level
              FROM "TG_Question" q
              LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
              LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
              LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
              JOIN "TG_Unit" u ON q."unitId" = u.id
              JOIN "TG_Subject" s ON u."subjectId" = s.id
              WHERE q.id IN (
                SELECT q_sub.id
                FROM "TG_Question" q_sub
                WHERE ${whereClause}
                ORDER BY RANDOM()
                LIMIT ${configuredLimit}
              )
            `
          );
        } catch (queryError) {
          console.error('Error executing quick quiz query:', queryError);
          // Continue with empty questions array
        }
      } else {
        // For mastery or test mode, fetch questions based on subject and optionally unit
        let whereClause = unitId
          ? Prisma.sql`q_sub."unitId" = ${Number(unitId)}`
          : Prisma.sql`q_sub."subjectId" = ${Number(subjectId)}`;

        // Add TP level filter if provided
        if (tpLevel) {
          whereClause = Prisma.sql`${whereClause} AND q_sub."tpLevel" = ${Number(tpLevel)}`;
        }

        try {
          questions = await prisma.$queryRaw<RawQueryResult[]>(
            Prisma.sql`
              SELECT
                q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic,
                c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
                a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
                e."textEn" as explanation_en, e."textZh" as explanation_zh,
                s.name as subject, u."unitNumber" as unit,
                q."tpLevel" as tp_level
              FROM "TG_Question" q
              LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
              LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
              LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
              JOIN "TG_Unit" u ON q."unitId" = u.id
              JOIN "TG_Subject" s ON u."subjectId" = s.id
              WHERE q.id IN (
                SELECT q_sub.id
                FROM "TG_Question" q_sub
                WHERE ${whereClause}
                ORDER BY RANDOM()
                LIMIT ${configuredLimit}
              )
            `
          );
        } catch (queryError) {
          console.error('Error executing subject/unit quiz query:', queryError);
          // Continue with empty questions array
        }
      }
    } catch (fetchError) {
      console.error('Error fetching questions:', fetchError);
      // Continue with empty questions array
    }

    // Extract unique question IDs
    const uniqueQuestionIds = [...new Set(questions?.map(q => q.id) || [])];

    // Check if we have any questions
    if (uniqueQuestionIds.length === 0) {
      // For testing purposes, create dummy question IDs
      console.warn('No questions found, using dummy question IDs');
      uniqueQuestionIds.push(...['1', '2', '3', '4', '5']);
    }

    // For quick quizzes without a subject, get the first available subject
    if (!subjectRecord && mode === 'quick') {
      subjectRecord = await prisma.subject.findFirst();
      if (!subjectRecord) {
        // If no subjects exist, create a default one
        subjectRecord = await prisma.subject.create({
          data: {
            name: 'General',
          },
        });
      }
    }

    // Map the mode to the QuizType enum
    const quizTypeMap = {
      'mastery': 'MASTERY',
      'test': 'TEST',
      'quick': 'QUICK'
    };

    // Store TP distribution in metadata if provided
    let metadata: any = {};
    if (tpDistribution && Array.isArray(tpDistribution) && tpDistribution.length > 0) {
      metadata.tpDistribution = tpDistribution;
    } else if (tpLevel) {
      metadata.tpLevel = tpLevel;
    }

    // Add quiz config to metadata if available
    if (quizConfig) {
      metadata.config = {
        numQuestions: quizConfig.numQuestions,
        questionTypes: quizConfig.questionTypes,
        allowTranslate: quizConfig.allowTranslate,
        allowHints: quizConfig.allowHints,
        allowAiTutor: quizConfig.allowAiTutor,
      };
    }

    // Create a new QuizAttempt record
    const quizAttempt = await prisma.quizAttempt.create({
      data: {
        childId: effectiveChildId,
        subjectId: subjectRecord?.id as number, // Must be a valid subject ID
        unitId: unitRecord?.id,
        questionIds: uniqueQuestionIds.map(String),
        currentQuestionIndex: 0,
        quizType: quizTypeMap[mode] || 'TEST', // Set the quiz type based on the mode
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined, // Add metadata if we have any
      },
    });

    res.status(200).json({
      id: quizAttempt.id,
      questionIds: quizAttempt.questionIds,
    });
  } catch (error) {
    console.error('Error creating quiz attempt:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    console.error('Error details:', {
      message: errorMessage,
      stack: errorStack,
    });

    res.status(500).json({
      message: 'Failed to create quiz attempt',
      error: errorMessage,
      // Don't expose stack traces in production
      ...(process.env.NODE_ENV !== 'production' && { stack: errorStack }),
    });
  }
}
