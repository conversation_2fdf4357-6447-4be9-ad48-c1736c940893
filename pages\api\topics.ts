import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';

/**
 * API endpoint for fetching topics/units for a specific subject
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { subjectId } = req.query;

  if (!subjectId || typeof subjectId !== 'string') {
    return res.status(400).json({ message: 'Subject ID is required' });
  }

  try {
    const subjectIdNum = parseInt(subjectId, 10);
    
    if (isNaN(subjectIdNum)) {
      return res.status(400).json({ message: 'Invalid Subject ID' });
    }

    const units = await prisma.unit.findMany({
      where: {
        subjectId: subjectIdNum,
      },
      orderBy: {
        unitNumber: 'asc',
      },
    });

    res.status(200).json(units);
  } catch (error) {
    console.error(`Error fetching topics for subject ${subjectId}:`, error);
    res.status(500).json({ message: 'Failed to fetch topics' });
  }
}
