import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';
import useAnswerSubmit from '../../hooks/useAnswerSubmit';
import { Question } from '../../types/quiz';

// Define the shape of our context
interface QuizV2ContextType {
  // State
  attempt: any;
  questions: Question[];
  currentIndex: number;
  selectedAnswer: string;
  submitting: boolean;
  isFinished: boolean;
  displayLanguage: 'en' | 'zh' | 'ms'; // For UI elements only
  childQuizLanguage: 'EN' | 'ZH' | 'MS'; // For question content
  childMenuLanguage: 'EN' | 'ZH' | 'MS'; // For menu language preference
  errorCount: number;
  explanation: string | null;
  isLoadingHint: boolean;
  isAnswerChecked: boolean;
  isAnswerCorrect: boolean | null;
  phase: 'PRIMARY' | 'REVIEW' | 'FINISHED';
  reviewQueue: Question[];
  firstTryCorrect: Record<number, boolean>;
  showKeywords: boolean;

  // Config flags
  allowTranslate: boolean;
  allowHints: boolean;
  allowAiTutor: boolean;
  allowReview: boolean;
  showDualLanguage: boolean; // Whether to show questions in dual language
  helpEnabled: boolean;

  // Actions
  setCurrentIndex: (index: number) => void;
  setSelectedAnswer: (answer: string) => void;
  setShowKeywords: (show: boolean) => void;
  submitAnswer: (question: Question, answer: string) => Promise<void>;
  toggleLanguage: () => void;
  getOriginalPrompt: (question: Question) => string;
  getQuestionContent: (question: Question) => string;
  getChoiceContent: (choice: any) => string;
  getSecondaryQuestionContent: (question: Question) => string;
  getSecondaryChoiceContent: (choice: any) => string;
  setErrorCount: (count: number) => void;
  setExplanation: (explanation: string | null) => void;
  fetchHint: (questionId: number) => Promise<void>;
  onMouseUp: (e: React.MouseEvent) => void;
  checkAnswer: (question: Question, answer: string) => Promise<void>;
  moveToNextQuestion: () => void;
  resetAnswerState: () => void;
}

// Create the context with a default undefined value
const QuizV2Context = createContext<QuizV2ContextType | undefined>(undefined);

// Props for the provider component
interface QuizV2ProviderProps {
  attempt: any;
  children: ReactNode;
}

// Provider component that will wrap the quiz components
export function QuizV2Provider({ attempt, children }: QuizV2ProviderProps) {
  const router = useRouter();
  const [currentIndex, setCurrentIndex] = useState(attempt?.quizAttempt?.currentQuestionIndex || 0);
  const [selectedAnswer, setSelectedAnswer] = useState('');

  // Initialize language preferences from child's settings
  const childQuizLanguage = attempt?.childLanguagePreferences?.quizLanguage || 'ZH';
  const childMenuLanguage = attempt?.childLanguagePreferences?.menuLanguage || 'EN';

  // Debug output
  console.log('QuizV2Provider - childLanguagePreferences:', attempt?.childLanguagePreferences);
  console.log('QuizV2Provider - childQuizLanguage:', childQuizLanguage);
  console.log('QuizV2Provider - childMenuLanguage:', childMenuLanguage);

  // Convert the child's menu language to lowercase for display language
  const getInitialDisplayLanguage = (): 'en' | 'zh' | 'ms' => {
    if (childMenuLanguage === 'EN') return 'en';
    if (childMenuLanguage === 'ZH') return 'zh';
    if (childMenuLanguage === 'MS') return 'ms';
    return 'en'; // Default to English if unknown
  };

  const [displayLanguage, setDisplayLanguage] = useState<'en' | 'zh' | 'ms'>(getInitialDisplayLanguage());
  const [isFinished, setIsFinished] = useState(attempt?.isCompleted || false);
  const [errorCount, setErrorCount] = useState(0);
  const [explanation, setExplanation] = useState<string | null>(null);
  const [isLoadingHint, setIsLoadingHint] = useState(false);
  const [isAnswerChecked, setIsAnswerChecked] = useState(false);
  const [isAnswerCorrect, setIsAnswerCorrect] = useState<boolean | null>(null);
  const [phase, setPhase] = useState<'PRIMARY' | 'REVIEW' | 'FINISHED'>('PRIMARY');
  const [reviewQueue, setReviewQueue] = useState<Question[]>([]);
  const [firstTryCorrect, setFirstTryCorrect] = useState<Record<number, boolean>>({});
  const [questions, setQuestions] = useState<Question[]>(attempt?.questions || []);
  const [showKeywords, setShowKeywords] = useState<boolean>(false);

  // Get config flags from metadata
  const configSnapshot = attempt?.quizAttempt?.metadata?.configSnapshot || {};
  const allowTranslate = configSnapshot.allowTranslate !== false;
  const allowHints = configSnapshot.allowHints !== false;
  const allowAiTutor = configSnapshot.allowAiTutor !== false;
  const allowReview = configSnapshot.reviewMissedQuestions !== false;
  const showDualLanguage = attempt?.childLanguagePreferences?.showDualLanguage === true;

  // Help features are only enabled in PRIMARY phase
  const helpEnabled = phase === 'PRIMARY';

  // Use the answer submit hook
  const { submit, submitting, fetchAiTutorExplanation } = useAnswerSubmit();

  // Function to check an answer without moving to the next question
  const checkAnswer = async (question: Question, answer: string) => {
    if (!answer || !question || submitting) return;

    try {
      const quizAttemptId = attempt?.quizAttempt?.id;
      if (!quizAttemptId) return;

      const timeSpent = 0; // We'll implement this later

      const result = await submit({
        attemptId: quizAttemptId,
        quizType: attempt?.quizAttempt?.quizType || 'MASTERY',
        question,
        selected: answer,
        timeSpent
      });

      // Set the answer checked state
      setIsAnswerChecked(true);
      setIsAnswerCorrect(result.correct);

      // Track first-try correctness for analytics
      if (phase === 'PRIMARY') {
        setFirstTryCorrect(prev => ({...prev, [question.id]: result.correct}));
      }

      // If the answer is incorrect, add to review queue (in PRIMARY phase only)
      if (!result.correct && phase === 'PRIMARY') {
        setReviewQueue(prev => [...prev, question]);
      }

      // If the answer is incorrect, increment error count and show explanation
      if (!result.correct) {
        setErrorCount(prev => prev + 1);

        // For incorrect answers, always fetch and show explanation
        try {
          // Get explanation from question if available
          if (question.explanation) {
            let explanationText = '';

            // Use displayLanguage instead of childQuizLanguage for explanations
            if (displayLanguage === 'zh' && question.explanation.zh) {
              explanationText = question.explanation.zh;
            } else if (displayLanguage === 'ms' && (question.explanation as any).ms) {
              explanationText = (question.explanation as any).ms;
            } else if (question.explanation.en) {
              explanationText = question.explanation.en;
            }

            if (explanationText) {
              setExplanation(explanationText);
            } else {
              // Fallback to AI explanation if question doesn't have one
              // For AI tutor, we only support 'en' and 'zh' languages currently
              const aiExplanation = await fetchAiTutorExplanation(
                question,
                answer,
                displayLanguage === 'zh' ? 'zh' : 'en' // Use displayLanguage
              );
              setExplanation(aiExplanation);
            }
          } else {
            // Fallback to AI explanation if question doesn't have one
            // For AI tutor, we only support 'en' and 'zh' languages currently
            const aiExplanation = await fetchAiTutorExplanation(
              question,
              answer,
              displayLanguage === 'zh' ? 'zh' : 'en' // Use displayLanguage
            );
            setExplanation(aiExplanation);
          }
        } catch (error) {
          console.error('Error fetching explanation:', error);
          // Show error message in the current display language
          if (displayLanguage === 'en') {
            setExplanation('Error fetching explanation. Please try again.');
          } else if (displayLanguage === 'ms') {
            setExplanation('Ralat mendapatkan penjelasan. Sila cuba lagi.');
          } else {
            setExplanation('获取解释时出错。请再试一次。');
          }
        }
      } else {
        // Reset error count if answer is correct
        setErrorCount(0);
        setExplanation(null);
      }

      return result;
    } catch (error) {
      console.error('Error checking answer:', error);
    }
  };

  // Function to reset question state for a new question
  const resetQuestionState = () => {
    resetAnswerState();
    setErrorCount(0);
    setShowKeywords(false); // Reset keyword highlighting when moving to new question
  };

  // Function to move to the next question
  const moveToNextQuestion = () => {
    // Reset the answer state
    resetAnswerState();

    // Check if this is the last question in the current phase
    if (currentIndex === questions.length - 1) {
      if (phase === 'PRIMARY') {
        // If we have questions in the review queue and review is allowed, transition to REVIEW phase
        if (allowReview && reviewQueue.length > 0) {
          setPhase('REVIEW');
          setQuestions([...reviewQueue]); // No need to shuffle, server will handle it
          setCurrentIndex(0);
          resetQuestionState();
          return;
        }
        // Otherwise, finish the quiz
        setPhase('FINISHED');
        setIsFinished(true);
      } else if (phase === 'REVIEW') {
        // If we're in REVIEW phase and at the last question, finish the quiz
        setPhase('FINISHED');
        setIsFinished(true);
      }

      // Navigate back to dashboard after a delay
      setTimeout(() => {
        // Include firstTryCorrect data in the completion request
        completeQuiz();
        router.push('/student-dashboard');
      }, 3000);
    } else {
      // Move to the next question
      setCurrentIndex(currentIndex + 1);
    }
  };

  // Function to complete the quiz and send analytics data
  const completeQuiz = async () => {
    try {
      const quizAttemptId = attempt?.quizAttempt?.id;
      if (!quizAttemptId) return;

      // Calculate scores
      const totalQuestions = attempt?.questions?.length || 0;
      const correctFirstTry = Object.values(firstTryCorrect).filter(Boolean).length;
      const scorePercentage = (correctFirstTry / totalQuestions) * 100;

      // Include firstTryCorrect data in the metadata
      await fetch('/api/quiz/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          quizAttemptId,
          score: scorePercentage,
          totalQuestions,
          metadata: {
            reviewStats: {
              firstTryCorrect,
              reviewedQuestions: reviewQueue.length
            }
          }
        }),
      });
    } catch (error) {
      console.error('Error completing quiz:', error);
    }
  };

  // Function to reset the answer state
  const resetAnswerState = () => {
    setSelectedAnswer('');
    setIsAnswerChecked(false);
    setIsAnswerCorrect(null);
    setExplanation(null);
    setShowKeywords(false); // Reset keyword highlighting
  };

  // Legacy function for backward compatibility
  const submitAnswer = async (question: Question, answer: string) => {
    return checkAnswer(question, answer);
  };

  // Function to fetch a hint for a question
  const fetchHint = async (questionId: number) => {
    // Only allow hints in PRIMARY phase and if allowHints is true
    if (!allowHints || !helpEnabled) return;

    setIsLoadingHint(true);

    try {
      const response = await fetch('/api/ai-tutor/hint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId,
          language: displayLanguage // Pass the current display language
        }),
      });

      if (!response.ok) {
        throw new Error(`Error fetching hint: ${response.statusText}`);
      }

      const data = await response.json();
      setExplanation(data.hint);
    } catch (error) {
      console.error('Error fetching hint:', error);
      // Show error message in the current display language
      if (displayLanguage === 'en') {
        setExplanation('Error fetching hint. Please try again.');
      } else if (displayLanguage === 'ms') {
        setExplanation('Ralat mendapatkan petunjuk. Sila cuba lagi.');
      } else {
        setExplanation('获取提示时出错。请再试一次。');
      }
    } finally {
      setIsLoadingHint(false);
    }
  };

  // Function to toggle the display language
  const toggleLanguage = () => {
    setDisplayLanguage(prev => {
      const newLanguage = prev === 'en' ? 'zh' : prev === 'zh' ? 'ms' : 'en';

      // If there's an explanation and we're on an incorrect answer, refresh the explanation
      if (explanation && isAnswerChecked && isAnswerCorrect === false) {
        const currentQuestion = questions[currentIndex];
        if (currentQuestion && currentQuestion.explanation) {
          // Update explanation based on new language
          let explanationText = '';

          if (newLanguage === 'zh' && currentQuestion.explanation.zh) {
            explanationText = currentQuestion.explanation.zh;
          } else if (newLanguage === 'ms' && (currentQuestion.explanation as any).ms) {
            explanationText = (currentQuestion.explanation as any).ms;
          } else if (currentQuestion.explanation.en) {
            explanationText = currentQuestion.explanation.en;
          }

          if (explanationText) {
            setExplanation(explanationText);
          }
        }
      }

      return newLanguage;
    });
  };

  // Function to get the original prompt based on the child's quiz language preference
  const getOriginalPrompt = (question: Question): string => {
    if (!question) return '';

    // Use the child's quiz language preference instead of the question's original language
    switch (childQuizLanguage) {
      case 'EN':
        return question.promptEn || '';
      case 'MS':
        return question.promptMs || question.promptEn || '';
      default: // Default to Chinese
        return question.promptZh || '';
    }
  };

  // Function to get question content based on the child's quiz language preference
  const getQuestionContent = (question: Question): string => {
    if (!question) return '';

    // Always use the child's quiz language preference for question content
    switch (childQuizLanguage) {
      case 'EN':
        return question.promptEn || '';
      case 'MS':
        return question.promptMs || question.promptEn || '';
      default: // Default to Chinese
        return question.promptZh || '';
    }
  };

  // Function to get choice content based on the child's quiz language preference
  const getChoiceContent = (choice: any): string => {
    if (!choice) return '';

    // Always use the child's quiz language preference for choice content
    switch (childQuizLanguage) {
      case 'EN':
        return choice.textEn || '';
      case 'MS':
        return choice.textMs || choice.textEn || '';
      default: // Default to Chinese
        return choice.textZh || '';
    }
  };

  // Function to get question content in the menu language (for dual language display)
  const getSecondaryQuestionContent = (question: Question): string => {
    if (!question) return '';

    // Use the child's menu language for secondary content
    switch (childMenuLanguage) {
      case 'EN':
        return question.promptEn || '';
      case 'MS':
        return question.promptMs || question.promptEn || '';
      case 'ZH':
        return question.promptZh || '';
      default:
        return '';
    }
  };

  // Function to get choice content in the menu language (for dual language display)
  const getSecondaryChoiceContent = (choice: any): string => {
    if (!choice) return '';

    // Use the child's menu language for secondary content
    switch (childMenuLanguage) {
      case 'EN':
        return choice.textEn || '';
      case 'MS':
        return choice.textMs || choice.textEn || '';
      case 'ZH':
        return choice.textZh || '';
      default:
        return '';
    }
  };

  // Save quiz state when navigating away
  useEffect(() => {
    if (!attempt?.quizAttempt?.id) return;

    const handleBeforeUnload = async () => {
      try {
        await fetch('/api/log-quiz-attempt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            quizAttemptId: attempt.quizAttempt.id,
            currentQuestionIndex: currentIndex,
          }),
        });
      } catch (error) {
        console.error('Error saving quiz progress:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Save progress on component unmount
      handleBeforeUnload();
    };
  }, [attempt?.quizAttempt?.id, currentIndex]);

  // Create a handler for mouse up events (for translation)
  const onMouseUp = React.useCallback((e: React.MouseEvent) => {
    if (!allowTranslate) return;

    const selectedText = window.getSelection()?.toString().trim();
    if (selectedText && selectedText.length > 0) {
      // Store the selection in a global variable for the translation hook
      if (window) {
        // @ts-ignore
        window.quizV2SelectedText = {
          text: selectedText,
          x: e.pageX,
          y: e.pageY
        };
      }

      // Trigger a custom event that the translation hook can listen for
      const event = new CustomEvent('quizV2TextSelected', {
        detail: {
          text: selectedText,
          x: e.pageX,
          y: e.pageY
        }
      });
      window.dispatchEvent(event);
    }
  }, [allowTranslate]);

  // The value that will be provided to consumers of this context
  const value: QuizV2ContextType = {
    attempt,
    questions,
    currentIndex,
    selectedAnswer,
    submitting,
    isFinished,
    displayLanguage,
    childQuizLanguage,
    childMenuLanguage,
    errorCount,
    explanation,
    isLoadingHint,
    isAnswerChecked,
    isAnswerCorrect,
    phase,
    reviewQueue,
    firstTryCorrect,
    allowTranslate,
    allowHints,
    allowAiTutor,
    allowReview,
    showDualLanguage,
    helpEnabled,
    showKeywords,
    setCurrentIndex,
    setSelectedAnswer,
    setShowKeywords,
    submitAnswer: submitAnswer as any, // Type assertion to fix TypeScript error
    toggleLanguage,
    getOriginalPrompt,
    getQuestionContent,
    getChoiceContent,
    getSecondaryQuestionContent,
    getSecondaryChoiceContent,
    setErrorCount,
    setExplanation,
    fetchHint,
    onMouseUp,
    checkAnswer: checkAnswer as any, // Type assertion to fix TypeScript error
    moveToNextQuestion,
    resetAnswerState
  };

  return (
    <QuizV2Context.Provider value={value}>
      {children}
    </QuizV2Context.Provider>
  );
}

// Custom hook to use the quiz context
export function useQuizV2() {
  const context = useContext(QuizV2Context);
  if (context === undefined) {
    throw new Error('useQuizV2 must be used within a QuizV2Provider');
  }
  return context;
}
