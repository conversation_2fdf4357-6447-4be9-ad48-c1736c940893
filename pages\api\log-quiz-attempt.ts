import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]"; // Corrected path

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { quizAttemptId, currentQuestionIndex, questionId, submittedAnswer, isCorrect, childId, score, totalQuestions, quizType, questionIds } = req.body;

  try {
    if (quizAttemptId) {
      // Update existing quiz attempt
      const updatedQuizAttempt = await prisma.quizAttempt.update({
        where: { id: Number(quizAttemptId) },
        data: {
          currentQuestionIndex: Number(currentQuestionIndex),
          endTime: currentQuestionIndex === totalQuestions - 1 ? new Date() : undefined,
          score: currentQuestionIndex === totalQuestions - 1 ? score : undefined,
          ...(currentQuestionIndex === totalQuestions - 1 && { status: 'COMPLETED' }),
          ...(questionId && submittedAnswer !== undefined && isCorrect !== undefined ? {
            studentAnswers: {
              create: {
                childId: Number(childId),
                questionId: Number(questionId),
                submittedAnswer: submittedAnswer,
                isCorrect: isCorrect,
              },
            },
          } : {}),
        },
        include: {
          studentAnswers: true,
        },
      });
      res.status(200).json({ message: 'Quiz attempt updated successfully', quizAttempt: updatedQuizAttempt });

    } else {
      // Create a new quiz attempt (for completed quizzes initially, now also for the start of a new quiz)
      // This part might need adjustment based on how the quiz starts - ideally, the attempt is created
      // when the quiz is generated via the questions API. This block might become less relevant
      // or used for a different purpose (e.g., logging a fully completed quiz submitted at once).
      // For now, keeping it to avoid breaking existing functionality if any.
      if (!childId || !quizType || !questionIds) {
         return res.status(400).json({ message: 'Missing required fields for new quiz attempt' });
      }

      // TODO: Need to confirm if creating a new attempt here is still the correct logic.
      // The quiz attempt should ideally be created when questions are fetched/quiz starts.
      // This block might be redundant or need adjustment.
      const newQuizAttempt = await prisma.quizAttempt.create({ // Corrected model name
        data: {
          childId: Number(childId),
          questionIds: questionIds, // Assuming questionIds is an array of strings
          currentQuestionIndex: 0, // Start at the first question
          // score and endTime will be set when the quiz is completed
          // Need subjectId and potentially unitId to create a QuizAttempt
          // This requires fetching Subject/Unit based on quiz parameters or passing them in.
          // Placeholder - this create will likely fail without subjectId
          subjectId: 1, // Placeholder - MUST BE FIXED
          // @ts-ignore - quizType is a valid field in the database but TypeScript doesn't know about it yet
          quizType: quizType?.toUpperCase() === 'MASTERY' ? 'MASTERY' :
                   quizType?.toUpperCase() === 'QUICK' ? 'QUICK' : 'TEST',
        },
      });
       res.status(200).json({ message: 'New quiz attempt created successfully', quizAttempt: newQuizAttempt });
    }

  } catch (error) {
    console.error('Error logging/updating quiz attempt:', error);
    res.status(500).json({ message: 'Error logging/updating quiz attempt' });
  }
}
