import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { highlightedText, question, targetLanguage = 'English' } = req.body;

  if (!highlightedText || !question) {
    return res.status(400).json({ message: 'Missing required parameters' });
  }

  // Check for OpenRouter Translator API key
  const openRouterTranslatorApiKey = process.env.OPENROUTER_TRANSLATOR_API_KEY;
  if (!openRouterTranslatorApiKey) {
    console.error('AI Translator: OpenRouter Translator API key not configured');
    return res.status(500).json({ message: 'OpenRouter Translator API key not configured' });
  }

  // Prepare prompt for LLM with target language
  const prompt = `Translate the following highlighted text to ${targetLanguage}. The text is from a question: "${question}". The highlighted text is: "${highlightedText}". Provide only the translated text in your response.`;

  console.log('AI Translator: Translation request', {
    originalText: highlightedText,
    targetLanguage,
    textLength: highlightedText.length
  });

  // Set up the request payload
  const selectedModel = 'google/gemini-2.0-flash-exp:free';
  const fallbackModels = ['qwen/qwen3-235b-a22b:free','deepseek/deepseek-chat-v3-0324:free', 'google/gemma-3-27b-it:free'];

  const requestPayload = {
    model: selectedModel,
    models: fallbackModels,
    messages: [
      { role: 'system', content: 'You are a helpful translator.' },
      { role: 'user', content: prompt }
    ],
    temperature: 0.7,
    max_tokens: 512
  };

  console.log('AI Translator: Using model', selectedModel, 'with fallbacks');

  try {
    // Query OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterTranslatorApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestPayload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('AI Translator: LLM translation failed', {
        status: response.status,
        statusText: response.statusText,
        error: errorData.error?.message || 'Unknown error'
      });
      return res.status(500).json({
        message: 'LLM translation failed',
        error: errorData.error?.message || 'Unknown error'
      });
    }

    const data = await response.json();
    const translatedText = data.choices?.[0]?.message?.content || 'Failed to get translation';
    const modelUsed = data.model || selectedModel;
    const promptTokens = data.usage?.prompt_tokens || 0;
    const completionTokens = data.usage?.completion_tokens || 0;
    const totalTokens = data.usage?.total_tokens || 0;

    console.log('AI Translator: Translation successful', {
      modelUsed,
      promptTokens,
      completionTokens,
      totalTokens,
      originalTextLength: highlightedText.length,
      translatedTextLength: translatedText.length
    });

    // Include more information in the response for debugging
    res.status(200).json({
      translatedText,
      debug: {
        modelUsed,
        promptTokens,
        completionTokens,
        totalTokens
      }
    });
  } catch (error) {
    console.error('AI Translator: Unexpected error', error);
    res.status(500).json({
      message: 'Unexpected error during translation',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
